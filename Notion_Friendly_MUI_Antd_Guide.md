# MUI vs Ant Design - Complete Configuration Guide 2024

## Theming & Tùy biến

**Tiêu chí | MUI | Ant Design**

Theming system
- MUI: ThemeProvider mạnh mẽ, sx prop, styled API
- Ant Design: Design Token System, ConfigProvider

Dark mode  
- MUI: <PERSON><PERSON><PERSON> hợp sẵn với palette.mode
- Ant Design: D<PERSON> dàng với algorithm: theme.darkAlgorithm

CSS-in-JS
- MUI: Native (Emotion), chuy<PERSON><PERSON> sang Pigment CSS  
- Ant Design: Native từ v5+ (CSS-in-JS)

Tailwind integration
- MUI: <PERSON><PERSON> kết hợp với @mui/system
- Ant Design: <PERSON>h<PERSON> hơn do xung đột class

Customization
- MUI: Linh hoạt cao với sx prop và styled
- Ant Design: Systematic với tokens và props

---

# MUI Configuration Levels Overview

MUI có 8 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao. 

**Nguyên tắc:** Phạm vi tác động càng rộng (global) thì ưu tiên càng thấp.

## MUI Priority Table

**Cấp | Tên | Phạm vi | Ưu tiên | Mô tả**

1️⃣ Default MUI Styles | Global | Thấp nhất | Styles mặc định của MUI

2️⃣ Theme Object | Global | ⬆️ | Config theme với createTheme()

3️⃣ ThemeProvider | App/Section | ⬆️ | Wrapper cung cấp theme

4️⃣ Component Overrides | Component type | ⬆️ | Override cho tất cả component cùng loại

5️⃣ Slots | Component architecture | ⬆️ | Thay thế component structure

6️⃣ styled() API | Component instance | ⬆️ | Tạo styled component

7️⃣ sx prop | Component instance | ⬆️ | Inline styling với theme

8️⃣ inline styles | Component instance | Cao nhất | CSS inline trực tiếp

---

## MUI Code Examples

### 1️⃣ Default MUI Styles

```
// MUI Button mặc định - không cần config gì
<Button variant="contained">
  Default Button
</Button>
// → Màu xanh #1976d2, padding 6px 16px, borderRadius 4px
```

### 2️⃣ Theme Object

```
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#ff5722', // Đổi màu primary thành cam
    }
  },
  typography: {
    button: {
      fontSize: '1.2rem', // Tăng font size cho button
    }
  }
});
```

### 3️⃣ ThemeProvider

```
import { ThemeProvider } from '@mui/material/styles';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <Button variant="contained">
        Themed Button
      </Button>
      {/* → Màu cam #ff5722, fontSize 1.2rem */}
    </ThemeProvider>
  );
}
```

### 4️⃣ Component Overrides

```
const theme = createTheme({
  components: {
    MuiButton: {
      defaultProps: {
        disableRipple: true, // Tắt ripple effect
      },
      styleOverrides: {
        root: {
          borderRadius: 12, // Bo góc 12px cho TẤT CẢ button
          textTransform: 'none', // Không uppercase
        },
        containedPrimary: {
          backgroundColor: '#9c27b0', // Màu tím cho contained primary
          '&:hover': {
            backgroundColor: '#7b1fa2',
          }
        }
      }
    }
  }
});

// Sử dụng:
<Button variant="contained">
  Override Button
</Button>
// → Màu tím, bo góc 12px, không ripple, không uppercase
```

### 5️⃣ Slots

```
import { styled } from '@mui/material/styles';

// Custom component để thay thế
const CustomButtonRoot = styled('div')(({ theme }) => ({
  background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
  border: 0,
  borderRadius: 20,
  color: 'white',
  padding: '10px 30px',
  cursor: 'pointer',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

function SlotExample() {
  return (
    <Button
      slots={{
        root: CustomButtonRoot, // Thay thế root element
      }}
      slotProps={{
        root: {
          className: 'my-gradient-button',
          'data-testid': 'custom-button'
        }
      }}
    >
      Slot Button
    </Button>
    // → Gradient background, borderRadius 20px, thay thế hoàn toàn structure
  );
}
```

### 6️⃣ styled() API

```
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
  border: 0,
  borderRadius: 25,
  boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
  color: 'white',
  height: 48,
  padding: '0 30px',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

// Sử dụng:
<StyledButton>
  Styled Button
</StyledButton>
// → Gradient xanh, shadow, hover scale, borderRadius 25px
```

### 7️⃣ sx prop

```
<Button
  variant="contained"
  sx={{
    bgcolor: 'success.main',     // Màu xanh lá
    color: 'white',
    borderRadius: 3,             // Bo góc 24px (3 * 8px)
    px: 4,                       // Padding x = 32px
    py: 1.5,                     // Padding y = 12px
    fontSize: '1.1rem',
    fontWeight: 'bold',
    textTransform: 'capitalize',
    boxShadow: 3,
    '&:hover': {
      bgcolor: 'success.dark',
      transform: 'translateY(-2px)',
      boxShadow: 6,
    }
  }}
>
  SX Button
</Button>
// → Màu xanh lá, shadow, hover effect, font bold
```

### 8️⃣ Inline styles

```
<Button
  variant="contained"
  style={{
    backgroundColor: '#e91e63',  // Màu hồng
    color: 'white',
    borderRadius: '50px',        // Bo góc tròn
    padding: '15px 40px',
    fontSize: '16px',
    fontWeight: '600',
    border: '2px solid #ad1457',
    boxShadow: '0 4px 8px rgba(233, 30, 99, 0.4)'
  }}
>
  Inline Button
</Button>
// → Màu hồng, bo góc tròn, border, shadow - ƯU TIÊN CAO NHẤT!
```

---

## MUI Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```
<StyledButton
  sx={{ bgcolor: 'warning.main' }}
  style={{ backgroundColor: 'red' }}
>
  Final Button
</StyledButton>
```

**Kết quả:** Button sẽ có màu **ĐỎ** vì `style` (inline) có ưu tiên cao nhất! 🔴

---

# Ant Design Configuration Levels

Ant Design có 7 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao.

## Antd Priority Table

**Cấp | Tên | Phạm vi | Ưu tiên | Mô tả**

1️⃣ Default Antd Styles | Global | Thấp nhất | Styles mặc định của Antd

2️⃣ Theme Config | Global | ⬆️ | Config theme với ConfigProvider

3️⃣ ConfigProvider | App/Section | ⬆️ | Provider cung cấp config global

4️⃣ CSS Variables Override | Global/Component | ⬆️ | Override CSS variables

5️⃣ Component Props | Component instance | ⬆️ | Props trực tiếp trên component

6️⃣ className/CSS | Component instance | ⬆️ | CSS classes custom

7️⃣ style prop | Component instance | Cao nhất | Inline styles

---

## Antd Code Examples

### 1️⃣ Default Antd Styles

```
import { Button } from 'antd';

// Antd Button mặc định - không cần config gì
<Button type="primary">
  Default Button
</Button>
// → Màu xanh #1677ff, height 32px, padding 4px 15px
```

### 2️⃣ Theme Config

```
import { ConfigProvider, theme } from 'antd';

const customTheme = {
  token: {
    // Seed Token - các giá trị cơ bản
    colorPrimary: '#00b96b',        // Màu primary
    borderRadius: 8,                // Bo góc
    fontSize: 16,                   // Font size
  },
  algorithm: theme.darkAlgorithm,   // Dark theme algorithm
  components: {
    // Component-specific tokens
    Button: {
      colorPrimary: '#ff4d4f',      // Màu riêng cho Button
      borderRadius: 12,             // Bo góc riêng cho Button
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={customTheme}>
      <Button type="primary">
        Themed Button
      </Button>
      {/* → Màu đỏ #ff4d4f, bo góc 12px */}
    </ConfigProvider>
  );
}
```

### 3️⃣ ConfigProvider

```
import { ConfigProvider, Button, DatePicker } from 'antd';
import viVN from 'antd/locale/vi_VN';

function App() {
  return (
    <ConfigProvider
      locale={viVN}                    // Ngôn ngữ
      direction="rtl"                  // Hướng text
      theme={{
        token: {
          colorPrimary: '#722ed1',
        }
      }}
      componentSize="large"            // Size mặc định cho tất cả component
      form={{
        validateMessages: {            // Custom validation messages
          required: '${label} là bắt buộc!',
        },
      }}
    >
      <Button type="primary">Nút Chính</Button>
      <DatePicker placeholder="Chọn ngày" />
      {/* → Tất cả component đều có size large, tiếng Việt, màu tím */}
    </ConfigProvider>
  );
}
```

---

## Key Takeaways

### MUI
- **Nguyên tắc CSS Cascade:** Specific > General
- **Phạm vi rộng = Ưu tiên thấp:** Global < Component Type < Instance
- **Slots đặc biệt:** Thay thế cả component, không chỉ style
- **sx prop:** Kết hợp theme system với inline styling
- **inline styles:** Luôn thắng, nhưng không nên lạm dụng

### Ant Design
- **Design Token System:** Antd sử dụng token-based theming
- **ConfigProvider mạnh mẽ:** Control locale, direction, size, validation
- **CSS Variables:** Flexible override với CSS custom properties
- **Component Props:** Rich API với nhiều props built-in
- **!important:** Thường cần dùng trong CSS để override Antd styles

---

## Quick Reference

### Khi nào dùng cấp nào?

#### MUI
**Theme Object** - Setup global design system - Brand colors, typography
**Component Overrides** - Consistent styling cho component type - Tất cả Button có border radius 8px
**Slots** - Thay đổi component structure - Custom input với icon
**styled()** - Reusable styled component - Primary button với gradient
**sx prop** - One-off styling - Margin, padding cho layout
**inline styles** - Debug hoặc dynamic values - Conditional styling

#### Ant Design
**Theme Config** - Setup design system - Brand colors, spacing, typography
**ConfigProvider** - App-wide settings - Locale, direction, component size
**CSS Variables** - Flexible theming - Dynamic theme switching
**Component Props** - Built-in variations - size, type, shape, danger
**CSS Classes** - Custom styling - Complex animations, gradients
**Inline styles** - Dynamic/conditional - Runtime color changes

---

**Kết luận:** Cả MUI và Ant Design đều có hệ thống config mạnh mẽ, chọn theo project requirements và team expertise!
