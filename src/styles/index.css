@layer base, mui, theme, components, utilities;

/**
Tailwind base styles
*/
@import "tailwindcss/preflight.css" layer(base);

@import './app-base.css';

/**
Tailwind theme styles
*/
@import "tailwindcss/theme.css" layer(theme);

/**
Tailwind utilities styles
*/
@import "tailwindcss/utilities.css" layer(utilities);


@layer utilities {
    [dir="rtl"] [class*="space-x-"] > :not([hidden]) ~ :not([hidden]) {
        --tw-space-x-reverse: 1;
    }
}

/**
Tailwind plugins
*/
@plugin "@tailwindcss/typography";
@plugin "@tailwindcss/aspect-ratio";
@plugin "../@fuse/tailwind/plugins/icon-size.js";

/**
Custom variants
*/
@custom-variant dark (&:where(.dark, .dark *));

/**
Theme variables
*/
@theme {

    --breakpoint-xs: 0px;
    --breakpoint-sm: 37.5rem; /* 600px */
    --breakpoint-md: 60rem; /* 960px */
    --breakpoint-lg: 80rem; /* 1280px */
    --breakpoint-xl: 120rem; /* 1920px */

    --container-3xs: 16rem;
    --container-2xs: 18rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;

    --text-xs: 0.5625rem;  /* 9px */
    --text-xs-line-height: calc(1 / 0.5625);

    --text-sm: 0.6875rem;  /* 11px */
    --text-sm-line-height: calc(1.25 / 0.6875);

    --text-md: 0.75rem;    /* 12px */
    --text-md-line-height: calc(1.25 / 0.75);

    --text-base: 0.8125rem;/* 13px */
    --text-base-line-height: calc(1.5 / 0.8125);

    --text-lg: 0.875rem;   /* 14px */
    --text-lg-line-height: calc(1.5 / 0.875);

    --text-xl: 1rem;       /* 16px */
    --text-xl-line-height: calc(1.5 / 1);

    --text-2xl: 1.125rem;  /* 18px */
    --text-2xl-line-height: calc(1.75 / 1.125);

    --text-3xl: 1.375rem;  /* 22px */
    --text-3xl-line-height: calc(1.75 / 1.375);

    --text-4xl: 1.5rem;    /* 24px */
    --text-4xl-line-height: calc(2 / 1.5);

    --text-5xl: 1.75rem;   /* 28px */
    --text-5xl-line-height: calc(2 / 1.75);

    --text-6xl: 2.25rem;   /* 36px */
    --text-6xl-line-height: calc(2 / 2.25);

    --text-7xl: 3rem;      /* 48px */
    --text-7xl-line-height: calc(2 / 3);

    --text-8xl: 3.5rem;    /* 56px */
    --text-8xl-line-height: calc(2 / 3.5);

    --text-9xl: 4rem;      /* 64px */
    --text-9xl-line-height: calc(2 / 4);

    --text-10xl: 5rem;     /* 80px */
    --text-10xl-line-height: calc(2 / 5);
}

.container {
    max-width: 100%;
    @media (width >= 40rem) { max-width: 40rem; }
    @media (width >= 48rem) { max-width: 48rem; }
    @media (width >= 64rem) { max-width: 64rem; }
    @media (width >= 80rem) { max-width: 70rem; }
    @media (width >= 96rem) { max-width: 70rem; }
}

.prose {
    font-size: var(--text-base);
    line-height: var(--text-base-line-height);
}
/**
Print styles
*/
@import './print.css';

/**
Tables styles
*/
@import './tables.css';
