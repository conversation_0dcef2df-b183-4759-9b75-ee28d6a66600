const request = async <Response>(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, options) => {
	let body: FormData | string | undefined = undefined;

	if (options?.body instanceof FormData) {
		body = options.body;
	} else if (options?.body) {
		body = JSON.stringify(options?.body);
	}

	const baseHeaders: {
		[key: string]: string;
	} =
		body instanceof FormData
			? {}
			: {
					'Content-Type': 'application/json'
				};

	console.log('18', options?.baseUrl);

	const baseUrl = options?.baseUrl === undefined ? process.env.NEXT_PUBLIC_API_ENDPOINT : options.baseUrl;
	const fullUrl = baseUrl + url;

	const res = await fetch(fullUrl, {
		...options,
		headers: {
			...baseHeaders,
			...options?.headers
		},
		body,
		method
	});

	return await res.json();
};

const http = {
	get<Response>(url: string, options?) {
		return request<Response>('GET', url, options);
	},
	post<Response>(url: string, body: any, options?) {
		return request<Response>('POST', url, { ...options, body });
	},
	put<Response>(url: string, body: any, options?) {
		return request<Response>('PUT', url, { ...options, body });
	},
	delete<Response>(url: string, options?) {
		return request<Response>('DELETE', url, { ...options });
	}
};

export default http;
