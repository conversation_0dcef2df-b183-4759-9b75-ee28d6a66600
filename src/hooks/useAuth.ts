import { useState, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSnackbar } from 'notistack';
import http from '@/lib/http';

interface AuthState {
	isLoading: boolean;
	error: string | null;
}

interface SignInData {
	user: string;
	password: string;
}

interface SignUpData {
	firstName: string;
	lastName: string;
	displayName: string;
	email: string;
	password: string;
	passwordConfirm: string;
}

interface ForgotPasswordData {
	email: string;
}

interface VerifyOTPData {
	email: string;
	code: string;
}

/**
 * Custom hook for authentication operations
 * Centralizes auth logic and provides consistent error handling
 */
export function useAuth() {
	const [state, setState] = useState<AuthState>({
		isLoading: false,
		error: null
	});

	const router = useRouter();
	const searchParams = useSearchParams();
	const { enqueueSnackbar } = useSnackbar();

	const setLoading = useCallback((loading: boolean) => {
		setState(prev => ({ ...prev, isLoading: loading }));
	}, []);

	const setError = useCallback((error: string | null) => {
		setState(prev => ({ ...prev, error }));
	}, []);

	const signIn = useCallback(async (data: SignInData) => {
		setLoading(true);
		setError(null);

		try {
			const response = await http.post('/api-web/auth/sign-in', data);

			if (response.statusCode === 200) {
				const redirectUri = searchParams.get('redirect_uri');
				router.push(redirectUri || 'https://maps.ots.vn/maps');
				enqueueSnackbar('Đăng nhập thành công!', { variant: 'success' });
				return { success: true };
			} 
			
			if (response.status === 401 && response.errorCode === 'EMAIL_NOT_VERIFIED') {
				router.push(`/otp?email=${encodeURIComponent(data.user)}`);
				return { success: false, requiresVerification: true };
			}

			const errorMessage = response?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';
			setError(errorMessage);
			return { success: false, error: errorMessage };

		} catch (error: any) {
			const errorMessage = error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.';
			setError(errorMessage);
			return { success: false, error: errorMessage };
		} finally {
			setLoading(false);
		}
	}, [router, searchParams, enqueueSnackbar, setLoading, setError]);

	const signUp = useCallback(async (data: SignUpData) => {
		setLoading(true);
		setError(null);

		try {
			const response = await http.post('/api-web/api-gateway/auth/sign-up', {
				email: data.email,
				first_name: data.firstName,
				last_name: data.lastName,
				username: data.displayName,
				password: data.password,
				password_confirm: data.passwordConfirm
			});

			if (response.statusCode === 201) {
				router.push(`/otp?email=${encodeURIComponent(response?.data?.email || data.email)}`);
				enqueueSnackbar('Đăng ký thành công! Vui lòng kiểm tra email để xác thực.', { variant: 'success' });
				return { success: true };
			}

			const errorMessage = response?.message || 'Đăng ký thất bại. Vui lòng thử lại.';
			setError(errorMessage);
			return { success: false, error: errorMessage };

		} catch (error: any) {
			const errorMessage = error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.';
			setError(errorMessage);
			return { success: false, error: errorMessage };
		} finally {
			setLoading(false);
		}
	}, [router, enqueueSnackbar, setLoading, setError]);

	const forgotPassword = useCallback(async (data: ForgotPasswordData) => {
		setLoading(true);
		setError(null);

		try {
			const response = await http.post('/api-web/api-gateway/auth/forgot-password', data);

			if (response.statusCode === 200) {
				router.push(`/reset-password?email=${encodeURIComponent(data.email)}`);
				enqueueSnackbar(response?.message || 'Email đặt lại mật khẩu đã được gửi!', { variant: 'success' });
				return { success: true };
			}

			if (response.status === 401 && response.errorCode === 'EMAIL_NOT_VERIFIED') {
				router.push(`/otp?email=${encodeURIComponent(data.email)}`);
				return { success: false, requiresVerification: true };
			}

			const errorMessage = response?.message || 'Có lỗi xảy ra. Vui lòng thử lại.';
			setError(errorMessage);
			return { success: false, error: errorMessage };

		} catch (error: any) {
			const errorMessage = error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.';
			setError(errorMessage);
			return { success: false, error: errorMessage };
		} finally {
			setLoading(false);
		}
	}, [router, enqueueSnackbar, setLoading, setError]);

	const verifyOTP = useCallback(async (data: VerifyOTPData) => {
		setLoading(true);
		setError(null);

		try {
			const response = await http.post('/api-web/api-gateway/auth/email/verify', data);

			if (response.statusCode === 201) {
				router.push('/sign-in');
				enqueueSnackbar('Xác thực email thành công!', { variant: 'success' });
				return { success: true };
			}

			const errorMessage = response?.message || 'Mã OTP không đúng. Vui lòng thử lại.';
			setError(errorMessage);
			return { success: false, error: errorMessage };

		} catch (error: any) {
			const errorMessage = error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.';
			setError(errorMessage);
			return { success: false, error: errorMessage };
		} finally {
			setLoading(false);
		}
	}, [router, enqueueSnackbar, setLoading, setError]);

	const sendOTP = useCallback(async (email: string) => {
		setLoading(true);
		setError(null);

		try {
			const response = await http.post('/api-web/api-gateway/auth/email/send-code-verify', { email });

			if (response.statusCode === 201) {
				enqueueSnackbar('Mã OTP đã được gửi!', { variant: 'success' });
				return { success: true };
			}

			const errorMessage = response?.message || 'Có lỗi xảy ra khi gửi mã OTP.';
			setError(errorMessage);
			return { success: false, error: errorMessage };

		} catch (error: any) {
			const errorMessage = error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.';
			setError(errorMessage);
			return { success: false, error: errorMessage };
		} finally {
			setLoading(false);
		}
	}, [enqueueSnackbar, setLoading, setError]);

	return {
		...state,
		signIn,
		signUp,
		forgotPassword,
		verifyOTP,
		sendOTP,
		clearError: () => setError(null)
	};
}
