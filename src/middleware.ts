import { NextRequest, NextResponse } from 'next/server';
import apiFetch from '@/utils/apiFetch';
import { cookies } from 'next/headers';

export async function middleware(request: NextRequest) {
	try {
		const cookieHeader = request.headers.get('cookie') || '';

		const res = await apiFetch('/api-web/auth/check-token', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Cookie: cookieHeader
			},
			cache: 'no-store'
		});

		const rawSetCookie = res.headers.get('set-cookie');
		const data = await res.json();

		if (rawSetCookie) {
			const parts = rawSetCookie.split(';').map((p) => p.trim());
			const [nameValue, ...attributes] = parts;
			const [name, rawValue] = nameValue.split('=');

			let expires: Date | undefined;
			for (const attr of attributes) {
				if (attr.toLowerCase().startsWith('expires=')) {
					expires = new Date(attr.substring(8));
					break;
				}
			}

			const cookieStore = await cookies();

			const value = decodeURIComponent(rawValue);

			cookieStore.set(name, value, {
				path: '/',
				httpOnly: true,
				sameSite: 'lax',
				secure: true,
				expires
			});
		}

		console.log(rawSetCookie);

		if (res.ok) {
			// return NextResponse.redirect('https://maps.ots.vn/maps');
		}
	} catch (error) {
		return NextResponse.next();
	}
	return NextResponse.next();
}

export const config = {
	matcher: ['/sign-in', '/sign-up']
};
