import { z } from 'zod';

// Common validation patterns
export const emailSchema = z
	.string()
	.min(1, 'Email là bắt buộc')
	.email('Email không hợp lệ')
	.max(254, 'Email quá dài');

export const passwordSchema = z
	.string()
	.min(1, 'Mật khẩu là bắt buộc')
	.min(8, 'Mật khẩu phải có ít nhất 8 ký tự')
	.max(128, 'Mật khẩu quá dài')
	.regex(
		/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
		'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt'
	);

export const nameSchema = z
	.string()
	.min(1, 'Tê<PERSON> là bắt buộc')
	.min(2, 'Tên phải có ít nhất 2 ký tự')
	.max(50, 'Tên quá dài')
	.regex(/^[a-zA-ZÀ-ỹ\s]+$/, 'Tên chỉ được chứa chữ cái và khoảng trắng');

export const usernameSchema = z
	.string()
	.min(1, 'Tên đăng nhập là bắt buộc')
	.min(3, 'Tên đăng nhập phải có ít nhất 3 ký tự')
	.max(30, 'Tên đăng nhập quá dài')
	.regex(/^[a-zA-Z0-9_]+$/, 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới');

export const otpSchema = z
	.string()
	.min(1, 'Mã OTP là bắt buộc')
	.length(6, 'Mã OTP phải có 6 chữ số')
	.regex(/^\d{6}$/, 'Mã OTP chỉ được chứa số');

// Form schemas
export const signInSchema = z.object({
	user: z
		.string()
		.min(1, 'Email hoặc tên đăng nhập là bắt buộc')
		.min(3, 'Quá ngắn - phải có ít nhất 3 ký tự'),
	password: z
		.string()
		.min(1, 'Mật khẩu là bắt buộc')
		.min(4, 'Mật khẩu quá ngắn - phải có ít nhất 4 ký tự'),
	remember: z.boolean().optional()
});

export const signUpSchema = z
	.object({
		firstName: nameSchema,
		lastName: nameSchema,
		displayName: usernameSchema,
		email: emailSchema,
		password: passwordSchema,
		passwordConfirm: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc'),
		acceptTermsConditions: z
			.boolean()
			.refine((val) => val === true, 'Bạn phải đồng ý với điều khoản và chính sách bảo mật')
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: 'Mật khẩu xác nhận không khớp',
		path: ['passwordConfirm']
	});

export const forgotPasswordSchema = z.object({
	email: emailSchema
});

export const resetPasswordSchema = z
	.object({
		email: emailSchema,
		code: otpSchema,
		password: passwordSchema,
		passwordConfirm: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc')
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: 'Mật khẩu xác nhận không khớp',
		path: ['passwordConfirm']
	});

export const otpVerificationSchema = z.object({
	email: emailSchema,
	code: otpSchema
});

// Type exports
export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type OTPVerificationFormData = z.infer<typeof otpVerificationSchema>;
