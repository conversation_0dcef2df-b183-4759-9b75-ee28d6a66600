import themesConfig from 'src/configs/themesConfig';

const themeOptions = [
	{
		id: 'Default',
		section: {
			main: themesConfig.default,
			navbar: themesConfig.defaultDark,
			toolbar: themesConfig.default,
			footer: themesConfig.defaultDark
		}
	},
	{
		id: 'Default Dark',
		section: {
			main: themesConfig.defaultDark,
			navbar: themesConfig.defaultDark,
			toolbar: themesConfig.defaultDark,
			footer: themesConfig.defaultDark
		}
	},
	{
		id: 'Charcoal Teal',
		section: {
			main: themesConfig.charcoalTeal,
			navbar: themesConfig.charcoalTealDark,
			toolbar: themesConfig.charcoalTeal,
			footer: themesConfig.charcoalTealDark
		}
	},
	{
		id: 'Charcoal Teal Dark',
		section: {
			main: themesConfig.charcoalTealDark,
			navbar: themesConfig.charcoalTealDark,
			toolbar: themesConfig.charcoalTealDark,
			footer: themesConfig.charcoalTealDark
		}
	},
	{
		id: 'Blue Silver',
		section: {
			main: themesConfig.darkBlueSilver,
			navbar: themesConfig.darkBlueSilverDark,
			toolbar: themesConfig.darkBlueSilver,
			footer: themesConfig.darkBlueSilverDark
		}
	},
	{
		id: 'Blue Silver Dark',
		section: {
			main: themesConfig.darkBlueSilverDark,
			navbar: themesConfig.darkBlueSilverDark,
			toolbar: themesConfig.darkBlueSilverDark,
			footer: themesConfig.darkBlueSilverDark
		}
	},
	{
		id: 'Slate Crimson',
		section: {
			main: themesConfig.slateCrimson,
			navbar: themesConfig.slateCrimsonDark,
			toolbar: themesConfig.slateCrimson,
			footer: themesConfig.slateCrimsonDark
		}
	},
	{
		id: 'Slate Crimson Dark',
		section: {
			main: themesConfig.slateCrimsonDark,
			navbar: themesConfig.slateCrimsonDark,
			toolbar: themesConfig.slateCrimsonDark,
			footer: themesConfig.slateCrimsonDark
		}
	},
	{
		id: 'Emarald Gold',
		section: {
			main: themesConfig.emeraldGold,
			navbar: themesConfig.emeraldGoldDark,
			toolbar: themesConfig.emeraldGold,
			footer: themesConfig.emeraldGoldDark
		}
	},
	{
		id: 'Emarald Gold Dark',
		section: {
			main: themesConfig.emeraldGoldDark,
			navbar: themesConfig.emeraldGoldDark,
			toolbar: themesConfig.emeraldGoldDark,
			footer: themesConfig.emeraldGoldDark
		}
	},
	{
		id: 'Indigo Coral',
		section: {
			main: themesConfig.indigoCoral,
			navbar: themesConfig.indigoCoralDark,
			toolbar: themesConfig.indigoCoral,
			footer: themesConfig.indigoCoralDark
		}
	},
	{
		id: 'Indigo Coral Dark',
		section: {
			main: themesConfig.indigoCoralDark,
			navbar: themesConfig.indigoCoralDark,
			toolbar: themesConfig.indigoCoralDark,
			footer: themesConfig.indigoCoralDark
		}
	},
	{
		id: 'Sky Blue Orange',
		section: {
			main: themesConfig.skyBlueOrange,
			navbar: themesConfig.skyBlueOrangeDark,
			toolbar: themesConfig.skyBlueOrange,
			footer: themesConfig.skyBlueOrangeDark
		}
	},
	{
		id: 'Sky Blue Orange Dark',
		section: {
			main: themesConfig.skyBlueOrangeDark,
			navbar: themesConfig.skyBlueOrangeDark,
			toolbar: themesConfig.skyBlueOrangeDark,
			footer: themesConfig.skyBlueOrangeDark
		}
	},
	{
		id: 'Soft Green Maroon',
		section: {
			main: themesConfig.softGreenMaroon,
			navbar: themesConfig.softGreenMaroonDark,
			toolbar: themesConfig.softGreenMaroon,
			footer: themesConfig.softGreenMaroonDark
		}
	},
	{
		id: 'Soft Green Maroon Dark',
		section: {
			main: themesConfig.softGreenMaroonDark,
			navbar: themesConfig.softGreenMaroonDark,
			toolbar: themesConfig.softGreenMaroonDark,
			footer: themesConfig.softGreenMaroonDark
		}
	},
	{
		id: 'Cool Grey Pink',
		section: {
			main: themesConfig.coolGreyPink,
			navbar: themesConfig.coolGreyPinkDark,
			toolbar: themesConfig.coolGreyPink,
			footer: themesConfig.coolGreyPinkDark
		}
	},
	{
		id: 'Cool Grey Pink Dark',
		section: {
			main: themesConfig.coolGreyPinkDark,
			navbar: themesConfig.coolGreyPinkDark,
			toolbar: themesConfig.coolGreyPinkDark,
			footer: themesConfig.coolGreyPinkDark
		}
	}
];

export default themeOptions;
