import apiFetch from '@/utils/apiFetch';

const authApiRequest = {
	login: (options) => apiFetch(`/api-web/auth/sign-in`, options),
	register: (options) => apiFetch(`/api-web/api-gateway/auth/sign-up`, options),
	verifyEmail: (options) => apiFetch(`/api-web/api-gateway/auth/email/verify`, options),
	sendVerifyEmail: (options) => apiFetch(`/api-web/api-gateway/auth/email/send-code-verify`, options)
};

export default authApiRequest;
