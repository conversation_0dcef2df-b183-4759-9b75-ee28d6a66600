// app/my-account/page.tsx
'use client';

import { Box, Drawer, List, ListItem, ListItemText, Tabs, Tab, Typography } from '@mui/material';
import { useState } from 'react';

const sections = ['Thông tin cá nhân', '<PERSON><PERSON><PERSON> mật', '<PERSON><PERSON>ch sử đăng nhập'];

export default function MyAccountPage() {
	const [tabIndex, setTabIndex] = useState(0);

	return (
		<Box display="flex">
			{/* Sidebar */}
			<Drawer
				variant="permanent"
				anchor="left"
			>
				<List>
					{sections.map((text, index) => (
						<ListItem
							button
							key={text}
							selected={tabIndex === index}
							onClick={() => setTabIndex(index)}
						>
							<ListItemText primary={text} />
						</ListItem>
					))}
				</List>
			</Drawer>

			{/* Main content */}
			<Box
				flex={1}
				padding={3}
			>
				{tabIndex === 0 && <Typography>Form cập nhật thông tin cá nhân</Typography>}
				{tabIndex === 1 && <Typography><PERSON><PERSON><PERSON> mật kh<PERSON>u, bật 2FA</Typography>}
				{tabIndex === 2 && <Typography>Lịch sử hoạt động</Typography>}
			</Box>
		</Box>
	);
}
