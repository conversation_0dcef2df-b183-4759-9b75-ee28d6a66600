'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { Box, Button, IconButton } from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import Link from '@fuse/core/Link';
import AuthLayout from 'src/components/auth/AuthLayout';
import OTPInput from 'src/components/auth/OTPInput';
import http from '@/lib/http';

function OtpPage() {
	const searchParams = useSearchParams();
	const rawEmail = searchParams.get('email');
	const email = rawEmail?.replace(/ /g, '+');

	const [errors, setError] = useState<{ type: string; message: string } | null>(null);
	const [otp, setOtp] = useState('');
	const [isVerifying, setIsVerifying] = useState(false);
	const router = useRouter();
	const { enqueueSnackbar } = useSnackbar();

	useEffect(() => {
		if (email) {
			handleSendOtp();
		}
	}, [email]);

	const handleSendOtp = async () => {
		try {
			const response = await http.post('/api-web/api-gateway/auth/email/send-code-verify', {
				email
			});

			if (response.statusCode === 201) {
				enqueueSnackbar('Đã gửi mã code', { variant: 'success' });
			} else {
				setError({
					type: 'manual',
					message: response.message || 'Có lỗi xảy ra. Vui lòng thử lại.'
				});
			}
		} catch (error) {
			setError({ type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
		}
	};

	const handleVerifyEmail = async () => {
		if (isVerifying || otp.length !== 6) return;

		setIsVerifying(true);
		setError(null);

		try {
			const response = await http.post('/api-web/api-gateway/auth/email/verify', {
				email,
				code: otp
			});

			if (response.statusCode === 201) {
				router.push('sign-in');
				return;
			}

			setError({
				type: 'manual',
				message: response?.message || 'Có lỗi xảy ra. Vui lòng thử lại.'
			});
		} catch (error) {
			setError({ type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
		} finally {
			setIsVerifying(false);
		}
	};

	return (
		<AuthLayout
			title="Xác thực tài khoản"
			subtitle={
				<div className=" space-y-2">
					<div className="text-gray-600">Mã OTP đã được gửi đến email</div>
					<div>{email}</div>
				</div>
			}
		>
			<div className="mt-8 space-y-6">
				<OTPInput
					value={otp}
					onChange={setOtp}
					error={errors?.message}
					onResend={handleSendOtp}
				/>

				<Button
					variant="contained"
					color="primary"
					size="large"
					fullWidth
					onClick={handleVerifyEmail}
					disabled={otp.length !== 6 || isVerifying}
				>
					{isVerifying ? 'Đang xác thực...' : 'Xác thực'}
				</Button>

				{/* Alternative Actions */}
				<div className="pt-4 border-t border-gray-200">
					<div className="flex flex-col items-center justify-between sm:flex-row sm:justify-between  sm:space-y-0 sm:space-x-6">
						<Link
							to="/sign-in"
							className="text-md font-medium"
						>
							Đăng nhập với email khác
						</Link>
						<Link
							to="/sign-up"
							className="text-md font-medium"
						>
							Tạo tài khoản mới
						</Link>
					</div>
				</div>
			</div>
		</AuthLayout>
	);
}

export default OtpPage;
