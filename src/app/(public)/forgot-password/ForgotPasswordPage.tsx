import Link from '@fuse/core/Link';
import AuthLayout from 'src/components/auth/AuthLayout';
import AuthJsForm from '@auth/forms/AuthJsForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
	title: 'Quên mật khẩu - GTEL Maps',
	description: 'Khôi phục mật khẩu GTEL Maps - Hệ thống bản đồ và điều hướng hàng đầu Việt Nam',
	keywords: 'quên mật khẩu, khô<PERSON> phục, GTEL Maps, bản đồ Việt Nam'
};

/**
 * Optimized forgot password page
 */
function ForgotPasswordPage() {
	return (
		<AuthLayout
			title="Quên mật khẩu"
			subtitle={
				<div className="space-y-2">
					<div>Nhập địa chỉ email của bạn để nhận liên kết đặt lại mật khẩu</div>
				</div>
			}
		>
			<AuthJsForm formType="forgotpassword" />
			<div className="flex flex-col items-center sm:flex-row pt-8">
				<span>Nhớ mật khẩu?</span>
				<Link
					className="ml-1 text-md font-medium"
					to="/sign-in"
				>
					Quay lại đăng nhập
				</Link>
			</div>
		</AuthLayout>
	);
}

export default ForgotPasswordPage;
