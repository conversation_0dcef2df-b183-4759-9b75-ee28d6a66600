import Link from '@fuse/core/Link';
import AuthLayout from 'src/components/auth/AuthLayout';
import AuthJsForm from '@auth/forms/AuthJsForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
	title: 'Đăng ký - GTEL Maps',
	description: 'Tạo tài khoản GTEL Maps - Hệ thống bản đồ và điều hướng hàng đầu Việt Nam',
	keywords: 'đăng ký, GTEL Maps, bản đồ Việt Nam, tạo tài khoản'
};

/**
 * Optimized sign up page with improved UX and performance
 */
function SignUpPage() {
	return (
		<AuthLayout
			title="Đăng ký"
			subtitle={
				<div className="flex flex-col items-center justify-center sm:flex-row sm:justify-between">
					<span>Đã có tài khoản?</span>
					<Link
						className="ml-1 text-md font-medium"
						to="/sign-in"
					>
						<PERSON><PERSON><PERSON> nhập ngay
					</Link>
				</div>
			}
		>
			<AuthJsForm formType="signup" />
		</AuthLayout>
	);
}

export default SignUpPage;
