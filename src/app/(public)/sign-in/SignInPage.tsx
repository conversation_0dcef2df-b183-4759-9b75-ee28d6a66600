import Link from '@fuse/core/Link';
import AuthLayout from 'src/components/auth/AuthLayout';
import AuthJsForm from '@auth/forms/AuthJsForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
	title: 'Đăng nhập - GTEL Maps',
	description: 'Đăng nhập vào GTEL Maps - Hệ thống bản đồ và điều hướng hàng đầu Việt Nam',
	keywords: 'đăng nhập, GTEL Maps, bản đồ Việt Nam, điều hướng'
};

/**
 * Optimized sign in page with improved UX and performance
 */
function SignInPage() {
	return (
		<AuthLayout
			title="Đăng nhập"
			subtitle={
				<div className="flex flex-col items-center justify-center sm:flex-row sm:justify-between">
					<span>Chưa có tài khoản?</span>
					<Link
						className="ml-1 text-md font-medium"
						to="/sign-up"
					>
						<PERSON><PERSON>ng ký ngay
					</Link>
				</div>
			}
		>
			<AuthJsForm formType="signin" />
		</AuthLayout>
	);
}

export default SignInPage;
