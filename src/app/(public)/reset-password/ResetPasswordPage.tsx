'use-client';

import Typography from '@mui/material/Typography';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import CardContent from '@mui/material/CardContent';
import AuthJsForm from '@auth/forms/AuthJsForm';
import { redirect, useSearchParams } from 'next/navigation';
import http from '@/lib/http';
import { useState } from 'react';
import { Alert } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

function ResetPasswordPage() {
	const searchParams = useSearchParams();
	const rawEmail = searchParams.get('email');
	const email = rawEmail?.replace(/ /g, '+');

	const [errors, setError] = useState<{ type: string; message: string } | null>(null);

	const handleSendOtp = async () => {
		try {
			const response = await http.post('/api-web/api-gateway/auth/email/send-code-verify', {
				email
			});

			if (response.statusCode === 201) {
				enqueueSnackbar('Đã gửi mã code', { variant: 'success' });
			} else {
				setError({
					type: 'manual',
					message: response.message || 'Có lỗi xảy ra. Vui lòng thử lại.'
				});
			}
		} catch (error) {
			setError({ type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
		}
	};

	return (
		<div className="flex min-w-0 flex-1 flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
			<Paper className="h-full w-full px-4 py-2 ltr:border-r-1 rtl:border-l-1 sm:h-auto sm:w-auto sm:rounded-xl sm:p-12 sm:shadow-sm md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none">
				<CardContent className="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
					<img
						className="w-12"
						src="/assets/images/logo/logo.svg"
						alt="logo"
						onClick={() => redirect('/sign-in')}
					/>

					<Typography className="mt-8 text-4xl font-extrabold leading-[1.25] tracking-tight">
						Reset password
					</Typography>

					<div className="mt-4 flex items-baseline font-medium">
						<Typography className=" text-gray-700 ">
							Nếu bạn không nhận được mã OTP, vui lòng kiểm tra lại email hoặc thư mục spam.
						</Typography>
					</div>

					<Box
						className="underline cursor-pointer text-blue-600 mt-4"
						onClick={handleSendOtp}
					>
						Resend code
					</Box>

					{errors?.message && (
						<Alert
							className="mt-4"
							severity="error"
							sx={(theme) => ({
								backgroundColor: theme.palette.error.light,
								color: theme.palette.error.dark
							})}
						>
							{errors?.message}
						</Alert>
					)}
					<AuthJsForm formType="resertpassword" />
				</CardContent>
			</Paper>

			<Box
				className="relative hidden h-full flex-auto items-center justify-center overflow-hidden p-16 md:flex lg:px-28"
				sx={{ backgroundColor: 'primary.main' }}
			>
				<svg
					className="pointer-events-none absolute inset-0"
					viewBox="0 0 960 540"
					width="100%"
					height="100%"
					preserveAspectRatio="xMidYMax slice"
					xmlns="http://www.w3.org/2000/svg"
				>
					<Box
						component="g"
						className="opacity-5"
						fill="none"
						stroke="currentColor"
						strokeWidth="100"
					>
						<circle
							r="234"
							cx="196"
							cy="23"
						/>
						<circle
							r="234"
							cx="790"
							cy="491"
						/>
					</Box>
				</svg>
				<Box
					component="svg"
					className="absolute -right-16 -top-16 opacity-20"
					sx={{ color: 'primary.light' }}
					viewBox="0 0 220 192"
					width="220px"
					height="192px"
					fill="none"
				>
					<defs>
						<pattern
							id="837c3e70-6c3a-44e6-8854-cc48c737b659"
							x="0"
							y="0"
							width="20"
							height="20"
							patternUnits="userSpaceOnUse"
						>
							<rect
								x="0"
								y="0"
								width="4"
								height="4"
								fill="currentColor"
							/>
						</pattern>
					</defs>
					<rect
						width="220"
						height="192"
						fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"
					/>
				</Box>

				<div className="relative z-10 w-full max-w-4xl">
					<div className="text-7xl font-bold leading-none text-gray-100">
						<div>Chào mừng đến với</div>
						<div>GTEL MAPS</div>
					</div>
					<div className="mt-6 text-lg leading-6 tracking-tight text-white">
						Bạn có cảm thấy mệt mỏi khi lạc đường hoặc bị kẹt xe không? Hãy nói lời tạm biệt với lo lắng về
						điều hướng với GTEL MAPS đối tác hành trình hoàn hảo dành riêng cho Việt Nam!
					</div>
					<div className="mt-8 flex items-center">
						<AvatarGroup
							sx={{
								'& .MuiAvatar-root': {
									borderColor: 'primary.main'
								}
							}}
						>
							<Avatar src="/assets/images/avatars/female-18.jpg" />
							<Avatar src="/assets/images/avatars/female-11.jpg" />
							<Avatar src="/assets/images/avatars/male-09.jpg" />
							<Avatar src="/assets/images/avatars/male-16.jpg" />
						</AvatarGroup>

						<div className="ml-4 font-medium tracking-tight text-white">
							Đã có hơn hàng nghìn người tham gia cùng chúng tôi, đến lượt của bạn
						</div>
					</div>
				</div>
			</Box>
		</div>
	);
}

export default ResetPasswordPage;
