'use client';

import { Snackbar<PERSON>rovider } from 'notistack';
import { useEffect, useMemo, Suspense, useCallback } from 'react';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { enUS } from 'date-fns/locale/en-US';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { Provider } from 'react-redux';
import ErrorBoundary from '@fuse/utils/ErrorBoundary';
import AppContext from 'src/contexts/AppContext';

import { FuseSettingsProvider } from '@fuse/core/FuseSettings/FuseSettingsProvider';
import { I18nProvider } from '@i18n/I18nProvider';
import store from '../store/store';
import MainThemeProvider from '../contexts/MainThemeProvider';
import { useRouter, useSearchParams } from 'next/navigation';

import http from '@/lib/http';
type AppProps = {
	children?: React.ReactNode;
};

/**
 * Auth check component - handles authentication logic
 */
function AuthChecker({ children }: { children: React.ReactNode }) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const redirectUri = searchParams.get('redirect_uri');

	const checkToken = useCallback(async () => {
		try {
			const res = await http.get('/api-web/auth/check-token');

			if (res.statusCode === 200) {
				// For external redirects, use window.location instead of router.push
				const targetUrl = redirectUri || 'https://maps.ots.vn/maps';

				if (targetUrl.startsWith('http')) {
					window.location.href = targetUrl;
				} else {
					router.push(targetUrl);
				}
			}
		} catch (err) {
			// Silently handle auth check errors
			console.debug('Auth check failed:', err);
		}
	}, [router, redirectUri]);

	useEffect(() => {
		checkToken();
	}, [checkToken]);

	return <>{children}</>;
}

/**
 * App providers wrapper - contains all context providers
 */
function AppProviders({ children }: { children: React.ReactNode }) {
	const contextValue = useMemo(() => ({}), []);

	return (
		<ErrorBoundary>
			<AppContext.Provider value={contextValue}>
				{/* Date Picker Localization Provider */}
				<LocalizationProvider
					dateAdapter={AdapterDateFns}
					adapterLocale={enUS}
				>
					{/* Redux Store Provider */}
					<Provider store={store}>
						<FuseSettingsProvider>
							<I18nProvider>
								{/* Theme Provider */}
								<MainThemeProvider>
									{/* Notistack Notification Provider */}
									<SnackbarProvider
										maxSnack={5}
										anchorOrigin={{
											vertical: 'bottom',
											horizontal: 'right'
										}}
										classes={{
											containerRoot: 'bottom-0 right-0 mb-13 md:mb-17 mr-2 lg:mr-20 z-99'
										}}
									>
										{children}
									</SnackbarProvider>
								</MainThemeProvider>
							</I18nProvider>
						</FuseSettingsProvider>
					</Provider>
				</LocalizationProvider>
			</AppContext.Provider>
		</ErrorBoundary>
	);
}

/**
 * The main App component with Suspense boundary
 */
function App(props: AppProps) {
	const { children } = props;

	return (
		<AppProviders>
			<Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
				<AuthChecker>{children}</AuthChecker>
			</Suspense>
		</AppProviders>
	);
}

export default App;
