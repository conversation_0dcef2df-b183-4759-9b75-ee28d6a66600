'use client';

import { ReactNode } from 'react';
import Paper from '@mui/material/Paper';
import CardContent from '@mui/material/CardContent';
import AuthBrandingSection from './AuthBrandingSection';

interface AuthLayoutProps {
	children: ReactNode;
	title: string;
	subtitle?: ReactNode;
	showLogo?: boolean;
	maxWidth?: 'xs' | 'sm' | 'md';
}

/**
 * Shared layout component for all authentication pages
 * Reduces code duplication and ensures consistent design
 */
function AuthLayout({ children, title, subtitle, showLogo = true, maxWidth = 'sm' }: AuthLayoutProps) {
	const maxWidthClass = {
		xs: 'max-w-80',
		sm: 'max-w-96',
		md: 'max-w-md'
	}[maxWidth];

	return (
		<div className="flex min-w-0 flex-1 flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
			{/* Left Panel - Form */}
			<Paper
				className="h-full w-full px-4 py-2 ltr:border-r-1 rtl:border-l-1 sm:h-auto sm:w-auto sm:rounded-xl sm:p-12 sm:shadow-sm md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none"
				role="main"
				aria-labelledby="auth-title"
			>
				<CardContent className={`mx-auto w-full ${maxWidthClass} sm:mx-0 sm:w-80`}>
					{showLogo && (
						<img
							className="w-12"
							src="/assets/images/logo/logo.svg"
							alt="GTEL Maps Logo"
							loading="eager"
							width={48}
							height={48}
						/>
					)}

					<h1
						id="auth-title"
						className="mt-8 text-4xl font-extrabold leading-[1.25] tracking-tight"
					>
						{title}
					</h1>

					{subtitle && (
						<div className="mt-0.5 flex items-baseline font-medium">
							<span className="text-gray-600">{subtitle}</span>
						</div>
					)}

					{children}
				</CardContent>
			</Paper>

			{/* Right Panel - Branding */}
			<AuthBrandingSection />
		</div>
	);
}

export default AuthLayout;
