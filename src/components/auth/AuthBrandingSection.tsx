import { memo } from 'react';
import Box from '@mui/material/Box';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';

/**
 * Memoized branding section for authentication pages
 * Prevents unnecessary re-renders and improves performance
 * No animations - simple and clean
 */
const AuthBrandingSection = memo(() => {
	return (
		<Box
			className="relative hidden h-full flex-auto items-center justify-center overflow-hidden p-16 md:flex lg:px-28"
			sx={{ backgroundColor: 'primary.main' }}
		>
			{/* Background Pattern */}
			<svg
				className="pointer-events-none absolute inset-0"
				viewBox="0 0 960 540"
				width="100%"
				height="100%"
				preserveAspectRatio="xMidYMax slice"
				xmlns="http://www.w3.org/2000/svg"
				aria-hidden="true"
			>
				<Box
					component="g"
					className="opacity-5"
					fill="none"
					stroke="currentColor"
					strokeWidth="100"
				>
					<circle r="234" cx="196" cy="23" />
					<circle r="234" cx="790" cy="491" />
				</Box>
			</svg>

			{/* Decorative Pattern */}
			<Box
				component="svg"
				className="absolute -right-16 -top-16 opacity-20"
				sx={{ color: 'primary.light' }}
				viewBox="0 0 220 192"
				width="220px"
				height="192px"
				fill="none"
				aria-hidden="true"
			>
				<defs>
					<pattern
						id="auth-pattern"
						x="0"
						y="0"
						width="20"
						height="20"
						patternUnits="userSpaceOnUse"
					>
						<rect x="0" y="0" width="4" height="4" fill="currentColor" />
					</pattern>
				</defs>
				<rect width="220" height="192" fill="url(#auth-pattern)" />
			</Box>

			{/* Main Content */}
			<div className="relative z-10 w-full max-w-4xl">
				<div>
					<h2 className="text-7xl font-bold leading-none text-gray-100">
						<div>Chào mừng đến với</div>
						<div className="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
							GTEL MAPS
						</div>
					</h2>
				</div>

				<p className="mt-6 text-lg leading-6 tracking-tight text-white/90">
					Bạn có cảm thấy mệt mỏi khi lạc đường hoặc bị kẹt xe không? Hãy nói lời tạm biệt với lo lắng về điều
					hướng với GTEL MAPS - đối tác hành trình hoàn hảo dành riêng cho Việt Nam!
				</p>

				<div className="mt-8 flex items-center">
					<AvatarGroup
						sx={{
							'& .MuiAvatar-root': {
								borderColor: 'primary.main',
								width: 40,
								height: 40
							}
						}}
						max={4}
					>
						<Avatar 
							src="/assets/images/avatars/female-18.jpg" 
							alt="User testimonial"
							loading="lazy"
						/>
						<Avatar 
							src="/assets/images/avatars/female-11.jpg" 
							alt="User testimonial"
							loading="lazy"
						/>
						<Avatar 
							src="/assets/images/avatars/male-09.jpg" 
							alt="User testimonial"
							loading="lazy"
						/>
						<Avatar 
							src="/assets/images/avatars/male-16.jpg" 
							alt="User testimonial"
							loading="lazy"
						/>
					</AvatarGroup>

					<div className="ml-4 font-medium tracking-tight text-white/90">
						Đã có hơn <span className="font-bold text-white">10,000+</span> người tham gia cùng chúng tôi, đến lượt của bạn
					</div>
				</div>
			</div>
		</Box>
	);
});

AuthBrandingSection.displayName = 'AuthBrandingSection';

export default AuthBrandingSection;
