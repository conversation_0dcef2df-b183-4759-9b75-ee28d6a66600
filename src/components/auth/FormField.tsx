import { forwardRef } from 'react';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import { Controller, Control, FieldPath, FieldValues } from 'react-hook-form';

interface FormFieldProps<T extends FieldValues> extends Omit<TextFieldProps, 'name' | 'error' | 'helperText'> {
	name: FieldPath<T>;
	control: Control<T>;
	rules?: object;
}

/**
 * Reusable form field component with built-in validation
 * Reduces boilerplate code in forms
 */
function FormField<T extends FieldValues>({
	name,
	control,
	rules,
	...textFieldProps
}: FormFieldProps<T>) {
	return (
		<Controller
			name={name}
			control={control}
			rules={rules}
			render={({ field, fieldState: { error } }) => (
				<TextField
					{...field}
					{...textFieldProps}
					error={!!error}
					helperText={error?.message}
					variant="outlined"
					fullWidth
					InputProps={{
						...textFieldProps.InputProps,
						'aria-describedby': error ? `${name}-error` : undefined,
					}}
					FormHelperTextProps={{
						id: error ? `${name}-error` : undefined,
						...textFieldProps.FormHelperTextProps,
					}}
				/>
			)}
		/>
	);
}

export default FormField;
