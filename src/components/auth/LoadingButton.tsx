import { forwardRef } from 'react';
import Button, { ButtonProps } from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';

interface LoadingButtonProps extends ButtonProps {
	loading?: boolean;
	loadingText?: string;
}

/**
 * Enhanced button component with loading state
 */
const LoadingButton = forwardRef<HTMLButtonElement, LoadingButtonProps>(
	({ loading = false, loadingText, children, disabled, ...props }, ref) => {
		return (
			<Button
				{...props}
				ref={ref}
				disabled={disabled || loading}
				startIcon={
					loading ? (
						<CircularProgress
							size={16}
							color="inherit"
							sx={{ mr: 1 }}
						/>
					) : (
						props.startIcon
					)
				}
			>
				{loading ? loadingText || 'Đang xử lý...' : children}
			</Button>
		);
	}
);

LoadingButton.displayName = 'LoadingButton';

export default LoadingButton;
