'use client';

import { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button } from '@mui/material';
import OtpInput from 'react-otp-input';

interface OTPInputProps {
	value: string;
	onChange: (value: string) => void;
	onComplete?: (value: string) => void;
	length?: number;
	disabled?: boolean;
	error?: string;
	onResend?: () => void;
	resendCooldown?: number;
}

/**
 * Enhanced OTP Input component with better UX
 */
function OTPInput({
	value,
	onChange,
	onComplete,
	length = 6,
	disabled = false,
	error,
	onResend,
	resendCooldown = 60
}: OTPInputProps) {
	const [countdown, setCountdown] = useState(0);

	useEffect(() => {
		if (value.length === length && onComplete) {
			onComplete(value);
		}
	}, [value, length, onComplete]);

	useEffect(() => {
		if (countdown > 0) {
			const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
			return () => clearTimeout(timer);
		}
	}, [countdown]);

	const handleResend = useCallback(() => {
		if (onResend && countdown === 0) {
			onResend();
			setCountdown(resendCooldown);
		}
	}, [onResend, countdown, resendCooldown]);

	return (
		<Box className="space-y-6">
			{/* OTP Input */}
			<Box className="flex justify-center">
				<OtpInput
					value={value}
					onChange={onChange}
					numInputs={length}
					inputType="tel"
					shouldAutoFocus
					inputStyle={{
						width: '2.5rem',
						height: '2.5rem',
						margin: '0 0.25rem',
						fontSize: '1.25rem',
						borderRadius: '0.375rem',
						border: error ? '2px solid #ef4444' : '2px solid #e5e7eb',
						backgroundColor: '#ffffff',
						color: '#1f2937',
						outline: 'none'
					}}
					renderInput={(props, index) => (
						<input
							{...props}
							aria-label={`OTP digit ${index + 1}`}
						/>
					)}
				/>
			</Box>

			{/* Error Message */}
			{error && (
				<div className="text-center">
					<Typography
						color="error"
						variant="body2"
						role="alert"
						aria-live="polite"
					>
						{error}
					</Typography>
				</div>
			)}

			{/* Resend Button */}
			{onResend && (
				<div className="flex flex-col items-center justify-center sm:flex-row sm:justify-end mt-4">
					<span className="text-gray-600">Không nhận được mã OTP?</span>
					<Button
						variant="text"
						onClick={handleResend}
						disabled={countdown > 0 || disabled}
						className="ml-1 text-md font-medium"
						aria-label={countdown > 0 ? `Resend in ${countdown} seconds` : 'Resend OTP code'}
					>
						{countdown > 0 ? `Gửi lại sau ${countdown}s` : 'Gửi lại mã'}
					</Button>
				</div>
			)}

			{/* Progress Indicator */}
			<Box className="flex justify-center space-x-1">
				{Array.from({ length }).map((_, index) => (
					<div
						key={index}
						className={`w-2 h-2 rounded-full ${index < value.length ? 'bg-blue-500' : 'bg-gray-300'}`}
					/>
				))}
			</Box>
		</Box>
	);
}

export default OTPInput;
