import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import http from '@/lib/http';

/**
 * Form Validation Schema
 */
const schema = z.object({
	user: z.string().email('Invalid email').nonempty('Email is required')
});

type FormType = {
	user: string;
};

function AuthJsCredentialsForgotFasswordForm() {
	const { control, formState, handleSubmit, setValue, setError } = useForm<FormType>({
		mode: 'onChange',
		resolver: zodResolver(schema)
	});

	const { enqueueSnackbar } = useSnackbar();

	const { isValid, dirtyFields, errors } = formState;

	const router = useRouter();

	async function onSubmit(formData: FormType) {
		try {
			const { user } = formData;

			const response = await http.post('/api-web/api-gateway/auth/forgot-password', { email: user });

			if (response.statusCode === 200) {
				router.push('/reset-password?email=' + user);
				enqueueSnackbar(response?.message, { variant: 'success' });
				return true;
			} else if (response.status === 401 && response.errorCode === 'EMAIL_NOT_VERIFIED') {
				router.push('/otp?email=' + user);
			}

			setError('root', {
				type: 'manual',
				message: response?.message || 'Đăng nhập thất bại. Vui lòng thử lại.'
			});
		} catch (error: any) {
			setError('root', {
				type: 'manual',
				message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.'
			});
		}

		return false;
	}

	return (
		<form
			name="loginForm"
			noValidate
			className="mt-8 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-8"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors?.root?.message}
				</Alert>
			)}
			<Controller
				name="user"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Email or Username"
						autoFocus
						type="text"
						error={!!errors.user}
						helperText={errors?.user?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>
			<Button
				variant="contained"
				color="secondary"
				className="mt-4 w-full"
				aria-label="Sign in"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Continue
			</Button>
		</form>
	);
}

export default AuthJsCredentialsForgotFasswordForm;
