import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import Link from '@fuse/core/Link';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import http from '@/lib/http';

/**
 * Form Validation Schema
 */
const schema = z.object({
	user: z
		.string()
		.nonempty('Please enter your email or Username .')
		.min(6, 'Email or Username is too short - must be at least 6 chars.'),
	password: z
		.string()
		.nonempty('Please enter your password.')
		.min(4, 'Password is too short - must be at least 4 chars.')
});

type FormType = {
	user: string;
	password: string;
	remember?: boolean;
};

const defaultValues = {
	user: '',
	password: '',
	remember: true
};

function AuthJsCredentialsSignInForm() {
	const { control, formState, handleSubmit, setValue, setError } = useForm<FormType>({
		mode: 'onChange',
		defaultValues,
		resolver: zodResolver(schema)
	});

	const { isValid, dirtyFields, errors } = formState;

	const router = useRouter();

	useEffect(() => {
		setValue('user', process.env.NEXT_PUBLIC_AUTH_USER, {
			shouldDirty: true,
			shouldValidate: true
		});
		setValue('password', process.env.NEXT_PUBLIC_AUTH_PASSWORD, {
			shouldDirty: true,
			shouldValidate: true
		});
	}, [setValue]);

	async function onSubmit(formData: FormType) {
		try {
			const { user, password } = formData;

			const response = await http.post('/api-web/auth/sign-in', { user, password });

			if (response.statusCode === 200) {
				// router.push('/dashboard');
				return true;
			} else if (response.status === 401 && response.errorCode === 'EMAIL_NOT_VERIFIED') {
				router.push('/otp?email=' + user);
			}

			setError('root', {
				type: 'manual',
				message: response?.message || 'Đăng nhập thất bại. Vui lòng thử lại.'
			});
		} catch (error: any) {
			setError('root', {
				type: 'manual',
				message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.'
			});
		}

		return false;
	}

	return (
		<form
			name="loginForm"
			noValidate
			className="mt-8 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-8"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors?.root?.message}
				</Alert>
			)}
			<Controller
				name="user"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Email or Username"
						autoFocus
						type="text"
						error={!!errors.user}
						helperText={errors?.user?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>
			<Controller
				name="password"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Password"
						type="password"
						error={!!errors.password}
						helperText={errors?.password?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>
			<div className="flex flex-col items-center justify-center sm:flex-row sm:justify-end">
				<Link
					className="text-md font-medium"
					to="/forgot-password"
				>
					Forgot password?
				</Link>
			</div>
			<Button
				variant="contained"
				color="secondary"
				className="mt-4 w-full"
				aria-label="Sign in"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Sign in
			</Button>
		</form>
	);
}

export default AuthJsCredentialsSignInForm;
