import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import http from '@/lib/http';
import { log } from 'console';
import { enqueueSnackbar } from 'notistack';

const schema = z
	.object({
		otp: z
			.string()
			.nonempty('OTP is required')
			.length(6, 'OTP must be exactly 6 digits')
			.regex(/^\d{6}$/, 'OTP must contain only digits'),

		password: z
			.string()
			.nonempty('Password is required')
			.min(6, 'Password must be at least 6 characters')
			.regex(
				/^(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9])/,
				'Password must have at least 1 uppercase letter, 1 number, and 1 symbol.'
			),
		passwordConfirm: z.string().nonempty('Password confirmation is required')
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: 'Passwords must match',
		path: ['passwordConfirm']
	});

const defaultValues = {
	otp: '',
	password: '',
	passwordConfirm: ''
};

export type FormType = {
	otp: string;
	password: string;
	passwordConfirm: string;
};

function AuthJsCredentialsResetPasswordForm() {
	const searchParams = useSearchParams();
	const rawEmail = searchParams.get('email');
	const email = rawEmail?.replace(/ /g, '+');

	console.log(email);

	const { control, formState, handleSubmit, setError } = useForm<FormType>({
		mode: 'onChange',
		defaultValues,
		resolver: zodResolver(schema)
	});

	const { isValid, dirtyFields, errors } = formState;

	const router = useRouter();

	async function onSubmit(formData: FormType) {
		try {
			const { otp, password, passwordConfirm } = formData;
			const response = await http.post('/api-web/api-gateway/auth/reset-password', {
				email,
				code: otp,
				new_password: password,
				new_password_confirm: passwordConfirm
			});

			console.log('Reset Password result:', response);

			if (response.statusCode === 200) {
				enqueueSnackbar('Đã gửi mã code', { variant: 'success' });
				router.push('/sign-in');
				return true;
			}

			setError('root', {
				type: 'manual',
				message: response?.message || 'Đăng k thất bại. Vui lòng thử lại.'
			});
		} catch (error) {
			setError('root', { type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
			return false;
		}
	}

	return (
		<form
			name="registerForm"
			noValidate
			className="mt-8 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-8"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors.root.message}
				</Alert>
			)}

			<Controller
				name="otp"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="OTP"
						type="text"
						error={!!errors.otp}
						helperText={errors?.otp?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="password"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Password"
						type="password"
						error={!!errors.password}
						helperText={errors?.password?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="passwordConfirm"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Password (Confirm)"
						type="password"
						error={!!errors.passwordConfirm}
						helperText={errors?.passwordConfirm?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>
			<Button
				variant="contained"
				color="secondary"
				className="mt-6 w-full"
				aria-label="Register"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Reset Password
			</Button>
		</form>
	);
}

export default AuthJsCredentialsResetPasswordForm;
