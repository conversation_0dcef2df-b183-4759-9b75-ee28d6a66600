import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Button from '@mui/material/Button';
import FormHelperText from '@mui/material/FormHelperText';
import { Alert } from '@mui/material';
import { useRouter } from 'next/navigation';
import http from '@/lib/http';

const schema = z
	.object({
		firstName: z.string().nonempty('First name is required'),
		lastName: z.string().nonempty('Last name is required'),
		displayName: z.string().nonempty('Display name is required'),
		email: z.string().email('Invalid email').nonempty('Email is required'),
		password: z
			.string()
			.nonempty('Password is required')
			.min(6, 'Password must be at least 6 characters')
			.regex(
				/^(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9])/,
				'Password must have at least 1 uppercase letter, 1 number, and 1 symbol.'
			),
		passwordConfirm: z.string().nonempty('Password confirmation is required'),
		acceptTermsConditions: z.boolean().refine((val) => val === true, 'You must accept the terms and conditions.')
	})
	.refine((data) => data.password === data.passwordConfirm, {
		message: 'Passwords must match',
		path: ['passwordConfirm']
	});

const defaultValues = {
	firstName: '',
	lastName: '',
	displayName: '',
	email: '',
	password: '',
	passwordConfirm: '',
	acceptTermsConditions: false
};

export type FormType = {
	firstName: string;
	lastName: string;
	displayName: string;
	email: string;
	password: string;
	passwordConfirm: string;
	acceptTermsConditions: boolean;
};

function AuthJsCredentialsSignUpForm() {
	const { control, formState, handleSubmit, setError } = useForm<FormType>({
		mode: 'onChange',
		defaultValues,
		resolver: zodResolver(schema)
	});

	const { isValid, dirtyFields, errors } = formState;

	const router = useRouter();

	async function onSubmit(formData: FormType) {
		try {
			const response = await http.post('/api-web/api-gateway/auth/sign-up', {
				email: formData.email,
				first_name: formData.firstName,
				last_name: formData.lastName,
				username: formData.displayName,
				password: formData.password,
				password_confirm: formData.passwordConfirm
			});

			if (response.statusCode === 201) {
				router.push('/otp?email=' + response?.data?.email);
				return true;
			}

			setError('root', {
				type: 'manual',
				message: response?.message || 'Đăng k thất bại. Vui lòng thử lại.'
			});
		} catch (error) {
			setError('root', { type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
			return false;
		}
	}

	return (
		<form
			name="registerForm"
			noValidate
			className="mt-8 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-8"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors.root.message}
				</Alert>
			)}

			<Controller
				name="firstName"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="First Name"
						type="text"
						error={!!errors.firstName}
						helperText={errors?.firstName?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="lastName"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Last Name"
						type="text"
						error={!!errors.lastName}
						helperText={errors?.lastName?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="displayName"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Display Name"
						type="text"
						error={!!errors.displayName}
						helperText={errors?.displayName?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="email"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Email"
						type="email"
						error={!!errors.email}
						helperText={errors?.email?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="password"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Password"
						type="password"
						error={!!errors.password}
						helperText={errors?.password?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="passwordConfirm"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-6"
						label="Password (Confirm)"
						type="password"
						error={!!errors.passwordConfirm}
						helperText={errors?.passwordConfirm?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="acceptTermsConditions"
				control={control}
				render={({ field }) => (
					<FormControl error={!!errors.acceptTermsConditions}>
						<FormControlLabel
							label="I agree with Terms and Privacy Policy"
							control={
								<Checkbox
									size="small"
									{...field}
								/>
							}
						/>
						<FormHelperText>{errors?.acceptTermsConditions?.message}</FormHelperText>
					</FormControl>
				)}
			/>

			<Button
				variant="contained"
				color="secondary"
				className="mt-6 w-full"
				aria-label="Register"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Create your free account
			</Button>
		</form>
	);
}

export default AuthJsCredentialsSignUpForm;
