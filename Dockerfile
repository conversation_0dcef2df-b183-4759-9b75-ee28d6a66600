FROM node:lts as base

ARG ENV_FILE=.env

# Update system
RUN apt-get update -y

# Install basic packages
RUN npm i -g pnpm serve

# Reduce npm log spam and colour during install within Docker
ENV NPM_CONFIG_LOGLEVEL=warn
ENV NPM_CONFIG_COLOR=false

USER node

RUN mkdir -p /home/<USER>/app

COPY package*.json /home/<USER>/app/
COPY yarn.* /home/<USER>/app/
COPY pnpm-lock.* /home/<USER>/app/
COPY .npmrc /home/<USER>/app/

USER root

# ## Development #################################################################
# # Define a development target that installs devDeps and runs in dev mode
FROM base as development
WORKDIR /home/<USER>/app
# Switch to the node user vs. root
USER node
# Install (not ci) with dependencies, and for Linux vs. Linux Musl (which we use for -alpine)
RUN pnpm install
# Copy the source code over
COPY --chown=node:node . /home/<USER>/app/
COPY --chown=node:node ./${ENV_FILE} /home/<USER>/app/.env
# Expose port 3000
EXPOSE 3000
# Start the app in debug mode so we can attach the debugger
CMD ["pnpm", "run", "dev"]

## Production ##################################################################
# Also define a production target which doesn't use devDeps
FROM base as production
WORKDIR /home/<USER>/app
# Switch to the node user vs. root
USER node
# Copy the source code over
COPY --chown=node:node --from=development /home/<USER>/app/ /home/<USER>/app/
COPY --chown=node:node --from=development /home/<USER>/app/${ENV_FILE} /home/<USER>/app/.env
# Build the app
RUN pnpm build
# Serve your static site
CMD ["serve", "-s", "out"]

## Deploy ######################################################################
# Use a stable nginx image
FROM node:lts-alpine as deploy

WORKDIR /app
USER node

# Copy only necessary output from production stage
COPY --from=production /home/<USER>/app/.next .next
COPY --from=production /home/<USER>/app/public public
COPY --from=production /home/<USER>/app/package.json .
COPY --from=production /home/<USER>/app/node_modules node_modules

EXPOSE 3000

CMD ["node_modules/.bin/next", "start"]

