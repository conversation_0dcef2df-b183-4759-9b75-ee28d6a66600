services:
    app:
        image: ttungbmt/fe-accounts:${APP_VERSION}
        build:
            context: .
            dockerfile: ./Dockerfile
            target: development
        ports:
            - '${APP_HTTP_PORT:-3000}:3000'
        volumes:
            - .:/home/<USER>/app
            - /home/<USER>/app/node_modules
    traefik:
        image: traefik:v3.1
        ports:
            - target: 80
              published: 80
              protocol: tcp
              mode: ingress
        command:
            - --configFile=/etc/traefik/traefik.yml
        volumes:
            - ./traefik/traefik.yml:/etc/traefik/traefik.yml
            - ./traefik/dynamic:/etc/traefik/dynamic
