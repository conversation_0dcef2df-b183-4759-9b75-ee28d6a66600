# 🐜 Ant Design Configuration Levels - Complete Guide

## 🎯 Overview
Ant Design có 7 cấp config kh<PERSON><PERSON> nhau, theo thứ tự ưu tiên từ thấp đến cao. **Nguyên tắc:** Phạm vi tác động càng rộng (global) thì ưu tiên càng thấp.

---

## 📊 Priority Table

| Cấp | Tên | Phạm vi | Ưu tiên | Mô tả |
|-----|-----|---------|---------|-------|
| 1️⃣ | Default Antd Styles | Global | Thấp nhất | Styles mặc định của Antd |
| 2️⃣ | Theme Config | Global | ⬆️ | Config theme với ConfigProvider |
| 3️⃣ | ConfigProvider | App/Section | ⬆️ | Provider cung cấp config global |
| 4️⃣ | CSS Variables Override | Global/Component | ⬆️ | Override CSS variables |
| 5️⃣ | Component Props | Component instance | ⬆️ | Props trực tiếp trên component |
| 6️⃣ | className/CSS | Component instance | ⬆️ | CSS classes custom |
| 7️⃣ | style prop | Component instance | Cao nhất | Inline styles |

---

## 💻 Code Examples

### 1️⃣ Default Antd Styles
```tsx
import { Button } from 'antd';

// Antd Button mặc định - không cần config gì
<Button type="primary">
  Default Button
</Button>
// → Màu xanh #1677ff, height 32px, padding 4px 15px
```

### 2️⃣ Theme Config
```tsx
import { ConfigProvider, theme } from 'antd';

const customTheme = {
  token: {
    // Seed Token - các giá trị cơ bản
    colorPrimary: '#00b96b',        // Màu primary
    borderRadius: 8,                // Bo góc
    fontSize: 16,                   // Font size
  },
  algorithm: theme.darkAlgorithm,   // Dark theme algorithm
  components: {
    // Component-specific tokens
    Button: {
      colorPrimary: '#ff4d4f',      // Màu riêng cho Button
      borderRadius: 12,             // Bo góc riêng cho Button
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={customTheme}>
      <Button type="primary">
        Themed Button
      </Button>
      {/* → Màu đỏ #ff4d4f, bo góc 12px */}
    </ConfigProvider>
  );
}
```

### 3️⃣ ConfigProvider
```tsx
import { ConfigProvider, Button, DatePicker } from 'antd';
import viVN from 'antd/locale/vi_VN';

function App() {
  return (
    <ConfigProvider
      locale={viVN}                    // Ngôn ngữ
      direction="rtl"                  // Hướng text
      theme={{
        token: {
          colorPrimary: '#722ed1',
        }
      }}
      componentSize="large"            // Size mặc định cho tất cả component
      form={{
        validateMessages: {            // Custom validation messages
          required: '${label} là bắt buộc!',
        },
      }}
    >
      <Button type="primary">Nút Chính</Button>
      <DatePicker placeholder="Chọn ngày" />
      {/* → Tất cả component đều có size large, tiếng Việt, màu tím */}
    </ConfigProvider>
  );
}
```

### 4️⃣ CSS Variables Override
```css
/* Global CSS override */
:root {
  --ant-color-primary: #eb2f96;           /* Override primary color */
  --ant-border-radius-base: 16px;         /* Override border radius */
  --ant-font-size-base: 15px;             /* Override font size */
}

/* Component-specific override */
.ant-btn {
  --ant-color-primary: #52c41a;           /* Chỉ cho Button */
  --ant-border-radius-base: 20px;
}

/* Custom CSS class */
.my-custom-button {
  --ant-color-primary: #faad14;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
```

```tsx
// Sử dụng:
<Button type="primary" className="my-custom-button">
  CSS Variables Button
</Button>
// → Màu vàng #faad14, bo góc 20px, có shadow
```

### 5️⃣ Component Props
```tsx
import { Button, Space } from 'antd';

function PropsExample() {
  return (
    <Space>
      <Button 
        type="primary"
        size="large"                    // Size lớn
        shape="round"                   // Hình dạng tròn
        danger                          // Màu đỏ nguy hiểm
        loading                         // Hiển thị loading
        icon={<SearchOutlined />}       // Icon
        block                           // Full width
      >
        Props Button
      </Button>
      
      <Button
        type="dashed"
        size="small"
        disabled
      >
        Disabled Button
      </Button>
    </Space>
  );
}
// → Button đầu: màu đỏ, size lớn, tròn, có icon, loading
// → Button thứ 2: dashed border, size nhỏ, disabled
```

### 6️⃣ className/CSS
```css
/* Custom CSS classes */
.gradient-button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
  border: none !important;
  color: white !important;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.gradient-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
}

.neon-button {
  background: transparent !important;
  border: 2px solid #00ffff !important;
  color: #00ffff !important;
  box-shadow: 
    0 0 10px #00ffff,
    inset 0 0 10px #00ffff !important;
  animation: neon-glow 2s infinite alternate;
}

@keyframes neon-glow {
  from { box-shadow: 0 0 10px #00ffff, inset 0 0 10px #00ffff; }
  to { box-shadow: 0 0 20px #00ffff, inset 0 0 20px #00ffff; }
}
```

```tsx
// Sử dụng:
<Space>
  <Button type="primary" className="gradient-button">
    Gradient Button
  </Button>
  
  <Button className="neon-button">
    Neon Button
  </Button>
</Space>
// → Button gradient với hover effect
// → Button neon với animation
```

### 7️⃣ style prop (Inline styles)
```tsx
<Button
  type="primary"
  style={{
    backgroundColor: '#9c27b0',        // Màu tím
    borderColor: '#7b1fa2',           // Border tím đậm
    color: 'white',
    height: '50px',                   // Chiều cao
    fontSize: '18px',                 // Font size
    fontWeight: 'bold',
    borderRadius: '25px',             // Bo góc tròn
    padding: '0 30px',                // Padding
    boxShadow: '0 6px 12px rgba(156, 39, 176, 0.3)',
    textTransform: 'uppercase',
    letterSpacing: '1px',
  }}
  onMouseEnter={(e) => {
    e.target.style.transform = 'scale(1.05)';
    e.target.style.boxShadow = '0 8px 16px rgba(156, 39, 176, 0.4)';
  }}
  onMouseLeave={(e) => {
    e.target.style.transform = 'scale(1)';
    e.target.style.boxShadow = '0 6px 12px rgba(156, 39, 176, 0.3)';
  }}
>
  Inline Style Button
</Button>
// → Màu tím, bo góc tròn, hover scale - ƯU TIÊN CAO NHẤT!
```

---

## 🎯 Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```tsx
// Theme config: màu xanh lá
<ConfigProvider theme={{ token: { colorPrimary: '#52c41a' } }}>
  {/* CSS class: màu vàng */}
  <Button 
    type="primary"
    className="my-yellow-button"
    style={{ backgroundColor: '#e91e63' }}  // Inline: màu hồng
  >
    Final Button
  </Button>
</ConfigProvider>
```

**Kết quả:** Button sẽ có màu **HỒNG** vì `style` (inline) có ưu tiên cao nhất! 🌸

---

## 🔑 Key Takeaways

- **Design Token System:** Antd sử dụng token-based theming
- **ConfigProvider mạnh mẽ:** Control locale, direction, size, validation
- **CSS Variables:** Flexible override với CSS custom properties
- **Component Props:** Rich API với nhiều props built-in
- **!important:** Thường cần dùng trong CSS để override Antd styles

---

## 📋 Quick Reference

### Khi nào dùng cấp nào?

| Cấp | Khi nào dùng | Ví dụ use case |
|-----|--------------|----------------|
| Theme Config | Setup design system | Brand colors, spacing, typography |
| ConfigProvider | App-wide settings | Locale, direction, component size |
| CSS Variables | Flexible theming | Dynamic theme switching |
| Component Props | Built-in variations | size, type, shape, danger |
| CSS Classes | Custom styling | Complex animations, gradients |
| Inline styles | Dynamic/conditional | Runtime color changes |

### Antd vs MUI Differences

| Aspect | Antd | MUI |
|--------|------|-----|
| Theme System | Token-based | Theme object |
| Provider | ConfigProvider | ThemeProvider |
| Customization | CSS Variables + Props | styled() + sx |
| Component API | Rich props | Minimal props + styling |
| Override Method | CSS classes + !important | styled() components |

---

*📝 Ghi chú: Antd focus vào component props và CSS variables, trong khi MUI focus vào styled components và sx prop. Cả hai đều mạnh mẽ nhưng approach khác nhau.*
