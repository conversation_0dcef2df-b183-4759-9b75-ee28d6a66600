# 🆚 MUI vs Ant Design - Updated Comparison 2024

## 7. Theming & Tùy biến

| Tiêu chí             | MUI                                               | Ant Design                                        |
| -------------------- | ------------------------------------------------- | ------------------------------------------------- |
| Theming system       | ✅ **ThemeProvider mạnh mẽ**, sx prop, styled API | ✅ **Design Token System**, ConfigProvider        |
| Dark mode            | ✅ **Tích hợp sẵn** với palette.mode              | ✅ **Dễ dàng** với algorithm: theme.darkAlgorithm |
| CSS-in-JS            | ✅ **Native (Emotion)**, chuyển sang Pigment CSS  | ✅ **Native từ v5+** (CSS-in-JS)                  |
| Tailwind integration | ✅ **D<PERSON> kết hợp** với @mui/system                 | ⚠️ **<PERSON>h<PERSON> hơn** do xung đột class                  |
| Customization        | ✅ **Linh hoạt cao** với sx prop và styled        | ✅ **Systematic** với tokens và props             |

---

# 🎯 MUI Configuration Levels Overview

MUI có 8 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao. **Nguyên tắc:** Phạm vi tác động càng rộng (global) thì ưu tiên càng thấp.

## 📊 Priority Table

| Cấp | Tên                 | Phạm vi                | Ưu tiên   | Mô tả                                   |
| --- | ------------------- | ---------------------- | --------- | --------------------------------------- |
| 1️⃣  | Default MUI Styles  | Global                 | Thấp nhất | Styles mặc định của MUI                 |
| 2️⃣  | Theme Object        | Global                 | ⬆️        | Config theme với createTheme()          |
| 3️⃣  | ThemeProvider       | App/Section            | ⬆️        | Wrapper cung cấp theme                  |
| 4️⃣  | Component Overrides | Component type         | ⬆️        | Override cho tất cả component cùng loại |
| 5️⃣  | Slots               | Component architecture | ⬆️        | Thay thế component structure            |
| 6️⃣  | styled() API        | Component instance     | ⬆️        | Tạo styled component                    |
| 7️⃣  | sx prop             | Component instance     | ⬆️        | Inline styling với theme                |
| 8️⃣  | inline styles       | Component instance     | Cao nhất  | CSS inline trực tiếp                    |

---

## 💻 Code Examples

### 1️⃣ Default MUI Styles

```tsx
// MUI Button mặc định - không cần config gì
<Button variant="contained">Default Button</Button>
// → Màu xanh #1976d2, padding 6px 16px, borderRadius 4px
```

### 2️⃣ Theme Object

```tsx
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
	palette: {
		primary: {
			main: '#ff5722' // Đổi màu primary thành cam
		}
	},
	typography: {
		button: {
			fontSize: '1.2rem' // Tăng font size cho button
		}
	}
});
```

### 3️⃣ ThemeProvider

```tsx
import { ThemeProvider } from '@mui/material/styles';

function App() {
	return (
		<ThemeProvider theme={theme}>
			<Button variant="contained">Themed Button</Button>
			{/* → Màu cam #ff5722, fontSize 1.2rem */}
		</ThemeProvider>
	);
}
```

### 4️⃣ Component Overrides

```tsx
const theme = createTheme({
	components: {
		MuiButton: {
			defaultProps: {
				disableRipple: true // Tắt ripple effect
			},
			styleOverrides: {
				root: {
					borderRadius: 12, // Bo góc 12px cho TẤT CẢ button
					textTransform: 'none' // Không uppercase
				},
				containedPrimary: {
					backgroundColor: '#9c27b0', // Màu tím cho contained primary
					'&:hover': {
						backgroundColor: '#7b1fa2'
					}
				}
			}
		}
	}
});

// Sử dụng:
<Button variant="contained">Override Button</Button>;
// → Màu tím, bo góc 12px, không ripple, không uppercase
```

### 5️⃣ Slots

```tsx
import { styled } from '@mui/material/styles';

// Custom component để thay thế
const CustomButtonRoot = styled('div')(({ theme }) => ({
	background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
	border: 0,
	borderRadius: 20,
	color: 'white',
	padding: '10px 30px',
	cursor: 'pointer',
	display: 'inline-flex',
	alignItems: 'center',
	justifyContent: 'center'
}));

function SlotExample() {
	return (
		<Button
			slots={{
				root: CustomButtonRoot // Thay thế root element
			}}
			slotProps={{
				root: {
					className: 'my-gradient-button',
					'data-testid': 'custom-button'
				}
			}}
		>
			Slot Button
		</Button>
		// → Gradient background, borderRadius 20px, thay thế hoàn toàn structure
	);
}
```

### 6️⃣ styled() API

```tsx
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
	background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
	border: 0,
	borderRadius: 25,
	boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
	color: 'white',
	height: 48,
	padding: '0 30px',
	'&:hover': {
		transform: 'scale(1.05)'
	}
}));

// Sử dụng:
<StyledButton>Styled Button</StyledButton>;
// → Gradient xanh, shadow, hover scale, borderRadius 25px
```

### 7️⃣ sx prop

```tsx
<Button
	variant="contained"
	sx={{
		bgcolor: 'success.main', // Màu xanh lá
		color: 'white',
		borderRadius: 3, // Bo góc 24px (3 * 8px)
		px: 4, // Padding x = 32px
		py: 1.5, // Padding y = 12px
		fontSize: '1.1rem',
		fontWeight: 'bold',
		textTransform: 'capitalize',
		boxShadow: 3,
		'&:hover': {
			bgcolor: 'success.dark',
			transform: 'translateY(-2px)',
			boxShadow: 6
		}
	}}
>
	SX Button
</Button>
// → Màu xanh lá, shadow, hover effect, font bold
```

### 8️⃣ Inline styles

```tsx
<Button
	variant="contained"
	style={{
		backgroundColor: '#e91e63', // Màu hồng
		color: 'white',
		borderRadius: '50px', // Bo góc tròn
		padding: '15px 40px',
		fontSize: '16px',
		fontWeight: '600',
		border: '2px solid #ad1457',
		boxShadow: '0 4px 8px rgba(233, 30, 99, 0.4)'
	}}
>
	Inline Button
</Button>
// → Màu hồng, bo góc tròn, border, shadow - ƯU TIÊN CAO NHẤT!
```

---

## 🎯 Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```tsx
<StyledButton
	sx={{ bgcolor: 'warning.main' }}
	style={{ backgroundColor: 'red' }}
>
	Final Button
</StyledButton>
```

**Kết quả:** Button sẽ có màu **ĐỎ** vì `style` (inline) có ưu tiên cao nhất! 🔴

---

## 🔑 Key Takeaways

-   **Nguyên tắc CSS Cascade:** Specific > General
-   **Phạm vi rộng = Ưu tiên thấp:** Global < Component Type < Instance
-   **Slots đặc biệt:** Thay thế cả component, không chỉ style
-   **sx prop:** Kết hợp theme system với inline styling
-   **inline styles:** Luôn thắng, nhưng không nên lạm dụng

---

## 📋 Quick Reference

### Khi nào dùng cấp nào?

| Cấp                 | Khi nào dùng                          | Ví dụ use case                     |
| ------------------- | ------------------------------------- | ---------------------------------- |
| Theme Object        | Setup global design system            | Brand colors, typography           |
| Component Overrides | Consistent styling cho component type | Tất cả Button có border radius 8px |
| Slots               | Thay đổi component structure          | Custom input với icon              |
| styled()            | Reusable styled component             | Primary button với gradient        |
| sx prop             | One-off styling                       | Margin, padding cho layout         |
| inline styles       | Debug hoặc dynamic values             | Conditional styling                |

---

# 🐜 Ant Design Configuration Levels

Ant Design có 7 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao.

## 📊 Antd Priority Table

| Cấp | Tên                    | Phạm vi            | Ưu tiên   | Mô tả                           |
| --- | ---------------------- | ------------------ | --------- | ------------------------------- |
| 1️⃣  | Default Antd Styles    | Global             | Thấp nhất | Styles mặc định của Antd        |
| 2️⃣  | Theme Config           | Global             | ⬆️        | Config theme với ConfigProvider |
| 3️⃣  | ConfigProvider         | App/Section        | ⬆️        | Provider cung cấp config global |
| 4️⃣  | CSS Variables Override | Global/Component   | ⬆️        | Override CSS variables          |
| 5️⃣  | Component Props        | Component instance | ⬆️        | Props trực tiếp trên component  |
| 6️⃣  | className/CSS          | Component instance | ⬆️        | CSS classes custom              |
| 7️⃣  | style prop             | Component instance | Cao nhất  | Inline styles                   |

---

## 🔥 What's New in 2024

### 🎨 **MUI Updates**

-   **Pigment CSS**: Moving from Emotion to zero-runtime CSS-in-JS
-   **Base UI**: Headless components for maximum flexibility
-   **Joy UI**: New design system alternative
-   **Material Design 3**: Updated to latest Material Design spec

### 🐜 **Ant Design Updates**

-   **Design Token System**: Complete rewrite of theming (v5+)
-   **CSS-in-JS**: Dropped Less, full CSS-in-JS adoption
-   **Component Token**: Granular component-level theming
-   **Algorithm**: Smart theme algorithms (dark, compact, etc.)

---

## 💻 Code Examples - Updated 2024

### **Theming Comparison**

#### MUI (2024)

```tsx
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
	palette: {
		mode: 'dark',
		primary: { main: '#1976d2' }
	},
	components: {
		MuiButton: {
			styleOverrides: {
				root: { borderRadius: 8 }
			}
		}
	}
});

<ThemeProvider theme={theme}>
	<Button sx={{ m: 2, bgcolor: 'primary.main' }}>MUI Button</Button>
</ThemeProvider>;
```

#### Ant Design (2024)

```tsx
import { ConfigProvider, theme } from 'antd';

<ConfigProvider
	theme={{
		algorithm: theme.darkAlgorithm,
		token: {
			colorPrimary: '#1976d2',
			borderRadius: 8
		},
		components: {
			Button: {
				colorPrimary: '#1976d2'
			}
		}
	}}
>
	<Button type="primary">Antd Button</Button>
</ConfigProvider>;
```

### **Dark Mode Comparison**

#### MUI

```tsx
const [mode, setMode] = useState('light');

const theme = createTheme({
	palette: {
		mode: mode // 'light' | 'dark'
	}
});

// Toggle function
const toggleMode = () => setMode(mode === 'light' ? 'dark' : 'light');
```

#### Ant Design

```tsx
const [isDark, setIsDark] = useState(false);

<ConfigProvider
	theme={{
		algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm
	}}
>
	{/* App content */}
</ConfigProvider>;

// Toggle function
const toggleTheme = () => setIsDark(!isDark);
```

### **Customization Comparison**

#### MUI - Maximum Flexibility

```tsx
// 1. sx prop
<Button sx={{
  bgcolor: 'primary.main',
  '&:hover': { bgcolor: 'primary.dark' }
}}>

// 2. styled API
const CustomButton = styled(Button)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
}));

// 3. slots system
<Button
  slots={{ root: 'div' }}
  slotProps={{ root: { className: 'custom-button' } }}
>
```

#### Ant Design - Systematic Approach

```tsx
// 1. Component tokens
<ConfigProvider
  theme={{
    components: {
      Button: {
        colorPrimary: '#ff4d4f',
        algorithm: true, // Enable algorithm
      }
    }
  }}
>

// 2. CSS Variables
<Button
  style={{
    '--ant-color-primary': '#ff4d4f',
    background: 'var(--ant-color-primary)'
  }}
>

// 3. Rich props
<Button
  type="primary"
  size="large"
  shape="round"
  danger
  loading
  icon={<SearchOutlined />}
>
```

---

## 🎯 Updated Recommendations 2024

### 🎨 **Choose MUI when:**

-   ✅ Building **custom design systems**
-   ✅ Need **maximum design flexibility**
-   ✅ Want **smaller bundle size**
-   ✅ Team comfortable with **CSS-in-JS**
-   ✅ Building **consumer-facing apps**
-   ✅ Need **Tailwind integration**

### 🐜 **Choose Ant Design when:**

-   ✅ Building **admin dashboards**
-   ✅ Need **rapid development**
-   ✅ Want **rich components** out-of-box
-   ✅ Need **enterprise features** (Table, Form, etc.)
-   ✅ Team prefers **configuration over customization**
-   ✅ Need **built-in internationalization**

---

## 📊 Performance Comparison 2024

| Metric                     | MUI                   | Ant Design          |
| -------------------------- | --------------------- | ------------------- |
| **Bundle size (min+gzip)** | ~80KB                 | ~120KB              |
| **Tree shaking**           | ✅ Excellent          | ✅ Good             |
| **Runtime performance**    | ✅ Fast (Pigment CSS) | ✅ Fast (CSS-in-JS) |
| **Build time**             | ✅ Fast               | ⚠️ Slower           |
| **Memory usage**           | ✅ Lower              | ⚠️ Higher           |

---

## 🏆 Final Verdict 2024

### **Overall Scores**

| Category                 | MUI   | Ant Design |
| ------------------------ | ----- | ---------- |
| **Flexibility**          | 9/10  | 7/10       |
| **Ease of Use**          | 6/10  | 9/10       |
| **Performance**          | 9/10  | 7/10       |
| **Component Richness**   | 6/10  | 10/10      |
| **Customization**        | 10/10 | 7/10       |
| **Developer Experience** | 8/10  | 8/10       |
| **Community**            | 9/10  | 8/10       |
| **Documentation**        | 9/10  | 8/10       |

### **Winner by Use Case:**

-   **🏆 Startup MVP:** Ant Design (faster development)
-   **🏆 Custom Brand:** MUI (unlimited flexibility)
-   **🏆 Enterprise Dashboard:** Ant Design (rich components)
-   **🏆 Design System:** MUI (component composition)
-   **🏆 Performance Critical:** MUI (smaller bundle)
-   **🏆 Rapid Prototyping:** Ant Design (rich props)

---

## 🔮 Future Outlook

### **MUI Roadmap**

-   **Pigment CSS**: Zero-runtime CSS-in-JS
-   **Base UI**: Headless component library
-   **Advanced theming**: Better design token support

### **Ant Design Roadmap**

-   **Design Token 2.0**: More granular control
-   **Performance**: Bundle size optimization
-   **Web Components**: Framework-agnostic components

---

_📝 Kết luận: Cả hai đều đã mature và excellent trong 2024. Chọn theo project requirements và team expertise!_
