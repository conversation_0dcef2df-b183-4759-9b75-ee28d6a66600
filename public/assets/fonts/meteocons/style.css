@font-face {
  font-family: 'meteocons';
  src:
    url('./fonts/meteocons.ttf?kx31oc') format('truetype'),
    url('./fonts/meteocons.woff?kx31oc') format('woff'),
    url('./fonts/meteocons.svg?kx31oc#meteocons') format('svg');
  font-weight: normal;
  font-style: normal;
}

.meteocons {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'meteocons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
