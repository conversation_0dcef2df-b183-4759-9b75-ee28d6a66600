# 📚 MUI Configuration Levels - Complete Guide

## 🎯 Overview
MUI có 8 cấp config kh<PERSON><PERSON> nhau, theo thứ tự ưu tiên từ thấp đến cao. **Nguyên tắc:** Phạm vi tác động càng rộng (global) thì ưu tiên càng thấp.

---

## 📊 Priority Table

| Cấp | Tên | Phạm vi | Ưu tiên | Mô tả |
|-----|-----|---------|---------|-------|
| 1️⃣ | Default MUI Styles | Global | Thấp nhất | Styles mặc định của MUI |
| 2️⃣ | Theme Object | Global | ⬆️ | Config theme với createTheme() |
| 3️⃣ | ThemeProvider | App/Section | ⬆️ | Wrapper cung cấp theme |
| 4️⃣ | Component Overrides | Component type | ⬆️ | Override cho tất cả component cùng loại |
| 5️⃣ | Slots | Component architecture | ⬆️ | Thay thế component structure |
| 6️⃣ | styled() API | Component instance | ⬆️ | Tạo styled component |
| 7️⃣ | sx prop | Component instance | ⬆️ | Inline styling với theme |
| 8️⃣ | inline styles | Component instance | Cao nhất | CSS inline trực tiếp |

---

## 💻 Code Examples

### 1️⃣ Default MUI Styles
```tsx
// MUI Button mặc định - không cần config gì
<Button variant="contained">
  Default Button
</Button>
// → Màu xanh #1976d2, padding 6px 16px, borderRadius 4px
```

### 2️⃣ Theme Object
```tsx
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#ff5722', // Đổi màu primary thành cam
    }
  },
  typography: {
    button: {
      fontSize: '1.2rem', // Tăng font size cho button
    }
  }
});
```

### 3️⃣ ThemeProvider
```tsx
import { ThemeProvider } from '@mui/material/styles';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <Button variant="contained">
        Themed Button
      </Button>
      {/* → Màu cam #ff5722, fontSize 1.2rem */}
    </ThemeProvider>
  );
}
```

### 4️⃣ Component Overrides
```tsx
const theme = createTheme({
  components: {
    MuiButton: {
      defaultProps: {
        disableRipple: true, // Tắt ripple effect
      },
      styleOverrides: {
        root: {
          borderRadius: 12, // Bo góc 12px cho TẤT CẢ button
          textTransform: 'none', // Không uppercase
        },
        containedPrimary: {
          backgroundColor: '#9c27b0', // Màu tím cho contained primary
          '&:hover': {
            backgroundColor: '#7b1fa2',
          }
        }
      }
    }
  }
});

// Sử dụng:
<Button variant="contained">
  Override Button
</Button>
// → Màu tím, bo góc 12px, không ripple, không uppercase
```

### 5️⃣ Slots
```tsx
import { styled } from '@mui/material/styles';

// Custom component để thay thế
const CustomButtonRoot = styled('div')(({ theme }) => ({
  background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
  border: 0,
  borderRadius: 20,
  color: 'white',
  padding: '10px 30px',
  cursor: 'pointer',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

function SlotExample() {
  return (
    <Button
      slots={{
        root: CustomButtonRoot, // Thay thế root element
      }}
      slotProps={{
        root: {
          className: 'my-gradient-button',
          'data-testid': 'custom-button'
        }
      }}
    >
      Slot Button
    </Button>
    // → Gradient background, borderRadius 20px, thay thế hoàn toàn structure
  );
}
```

### 6️⃣ styled() API
```tsx
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
  border: 0,
  borderRadius: 25,
  boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
  color: 'white',
  height: 48,
  padding: '0 30px',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

// Sử dụng:
<StyledButton>
  Styled Button
</StyledButton>
// → Gradient xanh, shadow, hover scale, borderRadius 25px
```

### 7️⃣ sx prop
```tsx
<Button
  variant="contained"
  sx={{
    bgcolor: 'success.main',     // Màu xanh lá
    color: 'white',
    borderRadius: 3,             // Bo góc 24px (3 * 8px)
    px: 4,                       // Padding x = 32px
    py: 1.5,                     // Padding y = 12px
    fontSize: '1.1rem',
    fontWeight: 'bold',
    textTransform: 'capitalize',
    boxShadow: 3,
    '&:hover': {
      bgcolor: 'success.dark',
      transform: 'translateY(-2px)',
      boxShadow: 6,
    }
  }}
>
  SX Button
</Button>
// → Màu xanh lá, shadow, hover effect, font bold
```

### 8️⃣ Inline styles
```tsx
<Button
  variant="contained"
  style={{
    backgroundColor: '#e91e63',  // Màu hồng
    color: 'white',
    borderRadius: '50px',        // Bo góc tròn
    padding: '15px 40px',
    fontSize: '16px',
    fontWeight: '600',
    border: '2px solid #ad1457',
    boxShadow: '0 4px 8px rgba(233, 30, 99, 0.4)'
  }}
>
  Inline Button
</Button>
// → Màu hồng, bo góc tròn, border, shadow - ƯU TIÊN CAO NHẤT!
```

---

## 🎯 Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```tsx
<StyledButton
  sx={{ bgcolor: 'warning.main' }}
  style={{ backgroundColor: 'red' }}
>
  Final Button
</StyledButton>
```

**Kết quả:** Button sẽ có màu **ĐỎ** vì `style` (inline) có ưu tiên cao nhất! 🔴

---

## 🔑 Key Takeaways

- **Nguyên tắc CSS Cascade:** Specific > General
- **Phạm vi rộng = Ưu tiên thấp:** Global < Component Type < Instance
- **Slots đặc biệt:** Thay thế cả component, không chỉ style
- **sx prop:** Kết hợp theme system với inline styling
- **inline styles:** Luôn thắng, nhưng không nên lạm dụng

---

## 📋 Quick Reference

### Khi nào dùng cấp nào?

| Cấp | Khi nào dùng | Ví dụ use case |
|-----|--------------|----------------|
| Theme Object | Setup global design system | Brand colors, typography |
| Component Overrides | Consistent styling cho component type | Tất cả Button có border radius 8px |
| Slots | Thay đổi component structure | Custom input với icon |
| styled() | Reusable styled component | Primary button với gradient |
| sx prop | One-off styling | Margin, padding cho layout |
| inline styles | Debug hoặc dynamic values | Conditional styling |

---

*📝 Ghi chú: Slots trong MUI cũng được coi là một cấp config theo leader của bạn - nó cho phép thay thế hoàn toàn component structure thay vì chỉ override styles.*
