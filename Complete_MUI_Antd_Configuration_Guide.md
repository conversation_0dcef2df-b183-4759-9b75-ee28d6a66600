# 🆚 MUI vs Ant Design - Complete Configuration Guide 2024

## 7. Theming & Tùy biến

| Tiêu chí             | MUI                                               | Ant Design                                        |
| -------------------- | ------------------------------------------------- | ------------------------------------------------- |
| Theming system       | ✅ **ThemeProvider mạnh mẽ**, sx prop, styled API | ✅ **Design Token System**, ConfigProvider        |
| Dark mode            | ✅ **Tích hợp sẵn** với palette.mode              | ✅ **Dễ dàng** với algorithm: theme.darkAlgorithm |
| CSS-in-JS            | ✅ **Native (Emotion)**, chuyển sang Pigment CSS  | ✅ **Native từ v5+** (CSS-in-JS)                  |
| Tailwind integration | ✅ **<PERSON><PERSON> kết hợp** với @mui/system                 | ⚠️ **<PERSON>h<PERSON> hơn** do xung đột class                  |
| Customization        | ✅ **Linh hoạt cao** với sx prop và styled        | ✅ **Systematic** với tokens và props             |

---

# 🎯 MUI Configuration Levels Overview

MUI có 8 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao. **Nguyên tắc:** Phạm vi tác động càng rộng (global) thì ưu tiên càng thấp.

## 📊 MUI Priority Table

| Cấp | Tên                 | Phạm vi                | Ưu tiên   | Mô tả                                   |
| --- | ------------------- | ---------------------- | --------- | --------------------------------------- |
| 1️⃣  | Default MUI Styles  | Global                 | Thấp nhất | Styles mặc định của MUI                 |
| 2️⃣  | Theme Object        | Global                 | ⬆️        | Config theme với createTheme()          |
| 3️⃣  | ThemeProvider       | App/Section            | ⬆️        | Wrapper cung cấp theme                  |
| 4️⃣  | Component Overrides | Component type         | ⬆️        | Override cho tất cả component cùng loại |
| 5️⃣  | Slots               | Component architecture | ⬆️        | Thay thế component structure            |
| 6️⃣  | styled() API        | Component instance     | ⬆️        | Tạo styled component                    |
| 7️⃣  | sx prop             | Component instance     | ⬆️        | Inline styling với theme                |
| 8️⃣  | inline styles       | Component instance     | Cao nhất  | CSS inline trực tiếp                    |

---

## 💻 MUI Code Examples

### 1️⃣ Default MUI Styles

```tsx
// MUI Button mặc định - không cần config gì
<Button variant="contained">Default Button</Button>
// → Màu xanh #1976d2, padding 6px 16px, borderRadius 4px
```

### 2️⃣ Theme Object

```tsx
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
	palette: {
		primary: {
			main: '#ff5722' // Đổi màu primary thành cam
		}
	},
	typography: {
		button: {
			fontSize: '1.2rem' // Tăng font size cho button
		}
	}
});
```

### 3️⃣ ThemeProvider

```tsx
import { ThemeProvider } from '@mui/material/styles';

function App() {
	return (
		<ThemeProvider theme={theme}>
			<Button variant="contained">Themed Button</Button>
			{/* → Màu cam #ff5722, fontSize 1.2rem */}
		</ThemeProvider>
	);
}
```

### 4️⃣ Component Overrides

```tsx
const theme = createTheme({
	components: {
		MuiButton: {
			defaultProps: {
				disableRipple: true // Tắt ripple effect
			},
			styleOverrides: {
				root: {
					borderRadius: 12, // Bo góc 12px cho TẤT CẢ button
					textTransform: 'none' // Không uppercase
				},
				containedPrimary: {
					backgroundColor: '#9c27b0', // Màu tím cho contained primary
					'&:hover': {
						backgroundColor: '#7b1fa2'
					}
				}
			}
		}
	}
});

// Sử dụng:
<Button variant="contained">Override Button</Button>;
// → Màu tím, bo góc 12px, không ripple, không uppercase
```

### 5️⃣ Slots

```tsx
import { styled } from '@mui/material/styles';

// Custom component để thay thế
const CustomButtonRoot = styled('div')(({ theme }) => ({
	background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
	border: 0,
	borderRadius: 20,
	color: 'white',
	padding: '10px 30px',
	cursor: 'pointer',
	display: 'inline-flex',
	alignItems: 'center',
	justifyContent: 'center'
}));

function SlotExample() {
	return (
		<Button
			slots={{
				root: CustomButtonRoot // Thay thế root element
			}}
			slotProps={{
				root: {
					className: 'my-gradient-button',
					'data-testid': 'custom-button'
				}
			}}
		>
			Slot Button
		</Button>
		// → Gradient background, borderRadius 20px, thay thế hoàn toàn structure
	);
}
```

### 6️⃣ styled() API

```tsx
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
	background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
	border: 0,
	borderRadius: 25,
	boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
	color: 'white',
	height: 48,
	padding: '0 30px',
	'&:hover': {
		transform: 'scale(1.05)'
	}
}));

// Sử dụng:
<StyledButton>Styled Button</StyledButton>;
// → Gradient xanh, shadow, hover scale, borderRadius 25px
```

### 7️⃣ sx prop

```tsx
<Button
	variant="contained"
	sx={{
		bgcolor: 'success.main', // Màu xanh lá
		color: 'white',
		borderRadius: 3, // Bo góc 24px (3 * 8px)
		px: 4, // Padding x = 32px
		py: 1.5, // Padding y = 12px
		fontSize: '1.1rem',
		fontWeight: 'bold',
		textTransform: 'capitalize',
		boxShadow: 3,
		'&:hover': {
			bgcolor: 'success.dark',
			transform: 'translateY(-2px)',
			boxShadow: 6
		}
	}}
>
	SX Button
</Button>
// → Màu xanh lá, shadow, hover effect, font bold
```

### 8️⃣ Inline styles

```tsx
<Button
	variant="contained"
	style={{
		backgroundColor: '#e91e63', // Màu hồng
		color: 'white',
		borderRadius: '50px', // Bo góc tròn
		padding: '15px 40px',
		fontSize: '16px',
		fontWeight: '600',
		border: '2px solid #ad1457',
		boxShadow: '0 4px 8px rgba(233, 30, 99, 0.4)'
	}}
>
	Inline Button
</Button>
// → Màu hồng, bo góc tròn, border, shadow - ƯU TIÊN CAO NHẤT!
```

---

## 🎯 MUI Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```tsx
<StyledButton
	sx={{ bgcolor: 'warning.main' }}
	style={{ backgroundColor: 'red' }}
>
	Final Button
</StyledButton>
```

**Kết quả:** Button sẽ có màu **ĐỎ** vì `style` (inline) có ưu tiên cao nhất! 🔴

---

# 🐜 Ant Design Configuration Levels

Ant Design có 7 cấp config khác nhau, theo thứ tự ưu tiên từ thấp đến cao.

## 📊 Antd Priority Table

| Cấp | Tên                    | Phạm vi            | Ưu tiên   | Mô tả                           |
| --- | ---------------------- | ------------------ | --------- | ------------------------------- |
| 1️⃣  | Default Antd Styles    | Global             | Thấp nhất | Styles mặc định của Antd        |
| 2️⃣  | Theme Config           | Global             | ⬆️        | Config theme với ConfigProvider |
| 3️⃣  | ConfigProvider         | App/Section        | ⬆️        | Provider cung cấp config global |
| 4️⃣  | CSS Variables Override | Global/Component   | ⬆️        | Override CSS variables          |
| 5️⃣  | Component Props        | Component instance | ⬆️        | Props trực tiếp trên component  |
| 6️⃣  | className/CSS          | Component instance | ⬆️        | CSS classes custom              |
| 7️⃣  | style prop             | Component instance | Cao nhất  | Inline styles                   |

---

## 💻 Antd Code Examples

### 1️⃣ Default Antd Styles

```tsx
import { Button } from 'antd';

// Antd Button mặc định - không cần config gì
<Button type="primary">Default Button</Button>;
// → Màu xanh #1677ff, height 32px, padding 4px 15px
```

### 2️⃣ Theme Config

```tsx
import { ConfigProvider, theme } from 'antd';

const customTheme = {
	token: {
		// Seed Token - các giá trị cơ bản
		colorPrimary: '#00b96b', // Màu primary
		borderRadius: 8, // Bo góc
		fontSize: 16 // Font size
	},
	algorithm: theme.darkAlgorithm, // Dark theme algorithm
	components: {
		// Component-specific tokens
		Button: {
			colorPrimary: '#ff4d4f', // Màu riêng cho Button
			borderRadius: 12 // Bo góc riêng cho Button
		}
	}
};

function App() {
	return (
		<ConfigProvider theme={customTheme}>
			<Button type="primary">Themed Button</Button>
			{/* → Màu đỏ #ff4d4f, bo góc 12px */}
		</ConfigProvider>
	);
}
```

### 3️⃣ ConfigProvider

```tsx
import { ConfigProvider, Button, DatePicker } from 'antd';
import viVN from 'antd/locale/vi_VN';

function App() {
	return (
		<ConfigProvider
			locale={viVN} // Ngôn ngữ
			direction="rtl" // Hướng text
			theme={{
				token: {
					colorPrimary: '#722ed1'
				}
			}}
			componentSize="large" // Size mặc định cho tất cả component
			form={{
				validateMessages: {
					// Custom validation messages
					required: '${label} là bắt buộc!'
				}
			}}
		>
			<Button type="primary">Nút Chính</Button>
			<DatePicker placeholder="Chọn ngày" />
			{/* → Tất cả component đều có size large, tiếng Việt, màu tím */}
		</ConfigProvider>
	);
}
```

### 4️⃣ CSS Variables Override

```css
/* Global CSS override */
:root {
	--ant-color-primary: #eb2f96; /* Override primary color */
	--ant-border-radius-base: 16px; /* Override border radius */
	--ant-font-size-base: 15px; /* Override font size */
}

/* Component-specific override */
.ant-btn {
	--ant-color-primary: #52c41a; /* Chỉ cho Button */
	--ant-border-radius-base: 20px;
}

/* Custom CSS class */
.my-custom-button {
	--ant-color-primary: #faad14;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
```

```tsx
// Sử dụng:
<Button
	type="primary"
	className="my-custom-button"
>
	CSS Variables Button
</Button>
// → Màu vàng #faad14, bo góc 20px, có shadow
```

### 5️⃣ Component Props

```tsx
import { Button, Space, SearchOutlined } from 'antd';

function PropsExample() {
	return (
		<Space>
			<Button
				type="primary"
				size="large" // Size lớn
				shape="round" // Hình dạng tròn
				danger // Màu đỏ nguy hiểm
				loading // Hiển thị loading
				icon={<SearchOutlined />} // Icon
				block // Full width
			>
				Props Button
			</Button>

			<Button
				type="dashed"
				size="small"
				disabled
			>
				Disabled Button
			</Button>
		</Space>
	);
}
// → Button đầu: màu đỏ, size lớn, tròn, có icon, loading
// → Button thứ 2: dashed border, size nhỏ, disabled
```

### 6️⃣ className/CSS

```css
/* Custom CSS classes */
.gradient-button {
	background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
	border: none !important;
	color: white !important;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	transition: all 0.3s ease;
}

.gradient-button:hover {
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
}
```

```tsx
// Sử dụng:
<Button
	type="primary"
	className="gradient-button"
>
	Gradient Button
</Button>
// → Button gradient với hover effect
```

### 7️⃣ style prop (Inline styles)

```tsx
<Button
	type="primary"
	style={{
		backgroundColor: '#9c27b0', // Màu tím
		borderColor: '#7b1fa2', // Border tím đậm
		color: 'white',
		height: '50px', // Chiều cao
		fontSize: '18px', // Font size
		fontWeight: 'bold',
		borderRadius: '25px', // Bo góc tròn
		padding: '0 30px', // Padding
		boxShadow: '0 6px 12px rgba(156, 39, 176, 0.3)',
		textTransform: 'uppercase',
		letterSpacing: '1px'
	}}
>
	Inline Style Button
</Button>
// → Màu tím, bo góc tròn, shadow - ƯU TIÊN CAO NHẤT!
```

---

## 🎯 Antd Final Example

Nếu áp dụng TẤT CẢ các cấp trên cho cùng 1 Button:

```tsx
// Theme config: màu xanh lá
<ConfigProvider theme={{ token: { colorPrimary: '#52c41a' } }}>
	{/* CSS class: màu vàng */}
	<Button
		type="primary"
		className="my-yellow-button"
		style={{ backgroundColor: '#e91e63' }} // Inline: màu hồng
	>
		Final Button
	</Button>
</ConfigProvider>
```

**Kết quả:** Button sẽ có màu **HỒNG** vì `style` (inline) có ưu tiên cao nhất! 🌸

---

## 🔥 What's New in 2024

### 🎨 **MUI Updates**

-   **Pigment CSS**: Moving from Emotion to zero-runtime CSS-in-JS
-   **Base UI**: Headless components for maximum flexibility
-   **Joy UI**: New design system alternative
-   **Material Design 3**: Updated to latest Material Design spec

### 🐜 **Ant Design Updates**

-   **Design Token System**: Complete rewrite of theming (v5+)
-   **CSS-in-JS**: Dropped Less, full CSS-in-JS adoption
-   **Component Token**: Granular component-level theming
-   **Algorithm**: Smart theme algorithms (dark, compact, etc.)

---

## 📊 Performance Comparison 2024

| Metric                     | MUI                   | Ant Design          |
| -------------------------- | --------------------- | ------------------- |
| **Bundle size (min+gzip)** | ~80KB                 | ~120KB              |
| **Tree shaking**           | ✅ Excellent          | ✅ Good             |
| **Runtime performance**    | ✅ Fast (Pigment CSS) | ✅ Fast (CSS-in-JS) |
| **Build time**             | ✅ Fast               | ⚠️ Slower           |
| **Memory usage**           | ✅ Lower              | ⚠️ Higher           |

---

## 🏆 Final Verdict 2024

### **Overall Scores**

| Category                 | MUI   | Ant Design |
| ------------------------ | ----- | ---------- |
| **Flexibility**          | 9/10  | 7/10       |
| **Ease of Use**          | 6/10  | 9/10       |
| **Performance**          | 9/10  | 7/10       |
| **Component Richness**   | 6/10  | 10/10      |
| **Customization**        | 10/10 | 7/10       |
| **Developer Experience** | 8/10  | 8/10       |
| **Community**            | 9/10  | 8/10       |
| **Documentation**        | 9/10  | 8/10       |

### **Winner by Use Case:**

-   **🏆 Startup MVP:** Ant Design (faster development)
-   **🏆 Custom Brand:** MUI (unlimited flexibility)
-   **🏆 Enterprise Dashboard:** Ant Design (rich components)
-   **🏆 Design System:** MUI (component composition)
-   **🏆 Performance Critical:** MUI (smaller bundle)
-   **🏆 Rapid Prototyping:** Ant Design (rich props)

---

## 🔑 Key Takeaways

### MUI

-   **Nguyên tắc CSS Cascade:** Specific > General
-   **Phạm vi rộng = Ưu tiên thấp:** Global < Component Type < Instance
-   **Slots đặc biệt:** Thay thế cả component, không chỉ style
-   **sx prop:** Kết hợp theme system với inline styling
-   **inline styles:** Luôn thắng, nhưng không nên lạm dụng

### Ant Design

-   **Design Token System:** Antd sử dụng token-based theming
-   **ConfigProvider mạnh mẽ:** Control locale, direction, size, validation
-   **CSS Variables:** Flexible override với CSS custom properties
-   **Component Props:** Rich API với nhiều props built-in
-   **!important:** Thường cần dùng trong CSS để override Antd styles

---

## 📋 Quick Reference

### Khi nào dùng cấp nào?

#### MUI

| Cấp                 | Khi nào dùng                          | Ví dụ use case                     |
| ------------------- | ------------------------------------- | ---------------------------------- |
| Theme Object        | Setup global design system            | Brand colors, typography           |
| Component Overrides | Consistent styling cho component type | Tất cả Button có border radius 8px |
| Slots               | Thay đổi component structure          | Custom input với icon              |
| styled()            | Reusable styled component             | Primary button với gradient        |
| sx prop             | One-off styling                       | Margin, padding cho layout         |
| inline styles       | Debug hoặc dynamic values             | Conditional styling                |

#### Ant Design

| Cấp             | Khi nào dùng        | Ví dụ use case                    |
| --------------- | ------------------- | --------------------------------- |
| Theme Config    | Setup design system | Brand colors, spacing, typography |
| ConfigProvider  | App-wide settings   | Locale, direction, component size |
| CSS Variables   | Flexible theming    | Dynamic theme switching           |
| Component Props | Built-in variations | size, type, shape, danger         |
| CSS Classes     | Custom styling      | Complex animations, gradients     |
| Inline styles   | Dynamic/conditional | Runtime color changes             |

---

## 🔮 Future Outlook

### **MUI Roadmap**

-   **Pigment CSS**: Zero-runtime CSS-in-JS
-   **Base UI**: Headless component library
-   **Advanced theming**: Better design token support

### **Ant Design Roadmap**

-   **Design Token 2.0**: More granular control
-   **Performance**: Bundle size optimization
-   **Web Components**: Framework-agnostic components

---

_📝 Kết luận: Cả MUI và Ant Design đều có hệ thống config mạnh mẽ, chọn theo project requirements và team expertise!_
